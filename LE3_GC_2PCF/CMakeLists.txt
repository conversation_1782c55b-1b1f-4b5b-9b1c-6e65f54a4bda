CMAKE_MINIMUM_REQUIRED(VERSION 2.8.5)

# Load elements_subdir to declare
# -------------------------------
elements_subdir(LE3_GC_2PCF)


# Load dependency from other module
# ---------------------------------
elements_depends_on_subdirs(ElementsKernel)


# Locate needed libraries
# -----------------------
find_package(Cfitsio REQUIRED)
find_package(GSL REQUIRED)


# Declare project library dependencies
# ------------------------------------
elements_add_library(LE3_GC_2PCF src/lib/*.cpp
                     LINK_LIBRARIES ElementsKernel
                                    LE3_GC_Libraries
                                    ${GSL_LIBRARIES}
                                    ${CFITSIO_LIBRARY}
                     INCLUDE_DIRS   ${CFITSIO_INCLUDE_DIR}
                     PUBLIC_HEADERS LE3_GC_2PCF)


# Declare the executable(s) with linked libraries
# -----------------------------------------------
elements_add_executable(LE3_GC_ComputeTwoPointCorrelation src/program/LE3_GC_ComputeTwoPointCorrelation.cpp
                     LINK_LIBRARIES ElementsKernel
                                    LE3_GC_2PCF
                                    LE3_GC_Libraries)


# Declare the Boost tests
# -----------------------
elements_add_unit_test(TwoPointCorrelation_test tests/src/TwoPointCorrelation_test.cpp
                     LINK_LIBRARIES LE3_GC_2PCF 
                                    LE3_GC_Libraries
                     TYPE Boost)
elements_add_unit_test(KdTree_test tests/src/KdTree_test.cpp
                     LINK_LIBRARIES LE3_GC_2PCF 
                                    LE3_GC_Libraries
                     TYPE Boost)
elements_add_unit_test(LinkedList_test tests/src/LinkedList_test.cpp
                     LINK_LIBRARIES LE3_GC_2PCF 
                                    LE3_GC_Libraries
                     TYPE Boost)

# Add configuration files
# -----------------------
elements_install_conf_files()
