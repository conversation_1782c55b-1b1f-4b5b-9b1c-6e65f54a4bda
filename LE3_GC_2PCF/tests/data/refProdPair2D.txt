<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<p1:DpdLe3TwoPointCorrGCPairs2D xmlns:p1="http://euclid.esa.org/schema/dpd/le3/gc">
  <Header>
    <ProductId>2</ProductId>
    <ProductType>XXXX</ProductType>
    <SoftwareName>XXXX</SoftwareName>
    <SoftwareRelease>XXXX</SoftwareRelease>
    <PipelineRun>XXXX</PipelineRun>
    <ExitStatusCode>XXXX</ExitStatusCode>
    <DataModelVersion>XXXX</DataModelVersion>
    <MinDataModelVersion>XXXX</MinDataModelVersion>
    <ScientificCustodian>LE3GC</ScientificCustodian>
    <AccessRights>
      <EuclidConsortiumRead>true</EuclidConsortiumRead>
      <EuclidConsortiumWrite>true</EuclidConsortiumWrite>
      <ScientificGroupRead>true</ScientificGroupRead>
      <ScientificGroupWrite>true</ScientificGroupWrite>
    </AccessRights>
    <Curator/>
    <Creator>LOCAL</Creator>
    <CreationDate>2018-01-04T13:25:38.273Z</CreationDate>
  </Header>
  <Data>
    <Statistics>TWO_DIM_CART</Statistics>
    <Pairs>DR</Pairs>
    <BinType_dim1>LIN</BinType_dim1>
    <BinMax_dim1>1</BinMax_dim1>
    <BinMin_dim1>11</BinMin_dim1>
    <BinNum_dim1>100</BinNum_dim1>
    <BinType_dim2>LOG</BinType_dim2>
    <BinMax_dim2>2</BinMax_dim2>
    <BinMin_dim2>22</BinMin_dim2>
    <BinNum_dim2>20</BinNum_dim2>
    <PairFile format="le3.gc.2pcf.pair2D" version="0.1">
      <DataContainer filestatus="PROPOSED">
        <FileName>example.file</FileName>
      </DataContainer>
    </PairFile>
  </Data>
</p1:DpdLe3TwoPointCorrGCPairs2D>
