<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<p1:DpdLE3gcTwoPointCorr2D xmlns:p1="http://euclid.esa.org/schema/dpd/le3/gc/corr2d">
  <Header>
    <ProductId>4</ProductId>
    <ProductType>XXXX</ProductType>
    <SoftwareName>XXXX</SoftwareName>
    <SoftwareRelease>XXXX</SoftwareRelease>
    <ManualValidationStatus>UNKNOWN</ManualValidationStatus>
    <PipelineRun>XXXX</PipelineRun>
    <ExitStatusCode>XXXX</ExitStatusCode>
    <DataModelVersion>XXXX</DataModelVersion>
    <MinDataModelVersion>XXXX</MinDataModelVersion>
    <ScientificCustodian>LE3GC</ScientificCustodian>
    <AccessRights>
      <EuclidConsortiumRead>true</EuclidConsortiumRead>
      <EuclidConsortiumWrite>true</EuclidConsortiumWrite>
      <ScientificGroupRead>true</ScientificGroupRead>
      <ScientificGroupWrite>true</ScientificGroupWrite>
    </AccessRights>
    <Curator/>
    <Creator>LOCAL</Creator>
    <CreationDate>2019-03-12T10:23:30.103Z</CreationDate>
  </Header>
  <Data>
    <Statistics>TWO_DIM_CART</Statistics>
    <BinTypeDim1>LIN</BinTypeDim1>
    <BinMaxDim1>1</BinMaxDim1>
    <BinMinDim1>11</BinMinDim1>
    <BinNumDim1>100</BinNumDim1>
    <BinTypeDim2>LOG</BinTypeDim2>
    <BinMaxDim2>1000</BinMaxDim2>
    <BinMinDim2>0.1</BinMinDim2>
    <BinNumDim2>5</BinNumDim2>
    <CorrFile format="le3.gc.2pcf.corr2D" version="0.1">
      <DataContainer filestatus="PROPOSED">
        <FileName>example.file</FileName>
      </DataContainer>
    </CorrFile>
  </Data>
</p1:DpdLE3gcTwoPointCorr2D>
