# ==============================================================================
#
#  Basic parameter file for the 2 point correlation function
#
#
# This parameter file has the following format:
# [Section.Subsection]
#    parameter_name = parameter_value
#
# Lines starting with # are considered to be a comment and ignored
#
# 2PCF Version >= 0.3.0
# =========================================================


# ---------------------------------------------------------------------------- #
#                         PARAMETERS FOR CATALOGUES
# ---------------------------------------------------------------------------- #
[Path]
  # path to input directory relative to workdir
  input  = data/
  # path to output directory relative to workdir
  output = data/


# ---------------------------------------------------------------------------- #
#                    PARAMETERS FOR CATALOGS
# ---------------------------------------------------------------------------- #
[Catalog]
# catalog informations
  constant_depth = true
  nbar_tabulated = true

  [Catalog.Galaxy]
  # input file for galaxy catalog with path relative to input_path
  name        = galaxy_coordinates.dat
  # Coordinate system [CARTESIAN, EUQATORIAL, PSEUDO_EQUATORIAL (ra,dec,z)]
  coordinates = CARTESIAN
  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates [DEGREES, RADIANS]
  angle_units = DEG
  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3
  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 4
  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5
  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 6


  [Catalog.Random]
  # input file for random catalog with path relative to input_path
  name        = random_coordinates.dat
  # Coordinate system [CARTESIAN, EUQATORIAL, PSEUDO_EQUATORIAL (ra,dec,z)]
  coordinates = CARTESIAN
  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates [DEGREES, RADIANS]
  angle_units = DEG
  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3
  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 4
  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5
  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 6


#------------------------------------------------------------------------------#
#             PARAMETERS FOR THE 2PT CORRELATION FUNCTION
#------------------------------------------------------------------------------#
[2PCF]
  # Correlation type [ONE_DIM, ANGULAR, TWO_DIM_CART, TWO_DIM_POLAR, PROJETED, MULTIPOLE]
  statistics = ONE_DIM
  # Pair counting method [LINKED_LIST, KD_TREE]
  method     = LINKED_LIST

  # Type of binning along 1st dimension [LINEAR, LOG]
  bin1_type  = LIN
# Number of bin channels in the 1st dimension (integer)
  bin1_num   = 100
  # Minimum value in the 1st dimension (double)
  bin1_min   = 0.1
  # Maximum value in the 1st dimension (double)
  bin1_max   = 10.

  # Type of binning along 2nd dimension [LINEAR, LOG]
  bin2_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin2_num   = 100
  # Minimum value in the 1st dimension (double)
  bin2_min   = 0.1
  # Maximum value in the 1st dimension (double)
  bin2_max   = 10.

  PI_MAX = 50.0

  # prefix name for output files
  output_prefix =

  # Input DD pairs FITS file (with path relative to workdir) to skip DD calculation
  input_file_DD =
  # Input DR pairs FITS file (with path relative to workdir) to skip DR calculation
  input_file_DR =
  # Input RR pairs FITS file (with path relative to workdir) to skip RR calculation
  input_file_RR =

  # Use object weights [true, false]
  use_weight = false

  # Random catalog dilution: % of the random catalog to keep: <float> (0,1]
  dilution = 0

  # enable legacy TXT output files
  enableTXT = true

[Other]
#Set coordinate system of galaxy cat. [0=CARTESIAN, 1= EUQATORIAL, 2=PSEUDOEQUATORIAL (ra,dec,z)]
sys_of_coord_g = 0
# Set units of angles for EQUATORIAL coordinates: [DEG, RAD]
angles_units_g = DEG

# Column number of the three coordinates in the DATA catalog
i_coord1_g = 1
i_coord2_g = 2
i_coord3_g = 3

# column number of mean density
i_mean_density_g = 4

# weight columns indexes
i_weight_g = 14
i_weight2_g = 15
i_weight3_g = 16
i_weight4_g = 17
# enable/disable weights
use_weight1_g = false
use_weight2_g = false
use_weight3_g = false
use_weight4_g = false

#
# RANDOM catalog
# [Random]

use_random_catalog = yes

#Set coordinate system of random cat.
sys_of_coord_r = 0
# Set units of angles for EQUATORIAL coordinates: [DEG, RAD]
angles_units_r = DEG

# Column number of the three coordinates in the catalog
i_coord1_r = 1
i_coord2_r = 2
i_coord3_r = 3

# column number of mean density
i_mean_density_r = 4

# weight columns indexes
i_weight_r = 14
i_weight2_r = 15
i_weight3_r = 16
i_weight4_r = 17

# enable/disable weights [true/false]
use_weight1_r = false
use_weight2_r = false
use_weight3_r = false
use_weight4_r = false

# BOTH CATALOGS: information for PK
# =========================================================
# Is the mean number density tabulated in the catalogs [true/false]
nbar_tabulated = false
# If not, has the survey a constant depth [true/false]
constant_depth = true
# Area of the survey in strad
area_survey = 15000



#------------------------------------------------------------------------------#
#             PARAMETERS FOR THE 2PT CORRELATION FUNCTION
#------------------------------------------------------------------------------#

# Type of correlation function [ONE_DIM, ANGULAR, TWO_DIM_CART, TWO_DIM_POLAR, PROJETED, MULTIPOLE]
Statistics = ONE_DIM
# Method used for pair counting [LINKED_LIST, KD_TREE]
Pair_counting_method = LINKED_LIST

# Dilution % of the random catalog <float> (0,1). Values 0 and 1 disable dilution
Dilution = 0

# Type of binning along 1st dimension [LINEAR, LOG]
Binning_type_dim1 = LINEAR
# Number of bin channels in the 1st dimension (integer)
N_BINS_DIM1 = 100
# Minimum value in the 1st dimension (double)
MIN_SCALE_DIM1 = 0.1
# Maximum value in the 1st dimension (double)
MAX_SCALE_DIM1 = 10.

# Type of binning along 2nd dimension [LINEAR, LOG]
Binning_type_dim2 = LINEAR
# Number of bin channels in the 2nd dimension (integer)
N_BINS_DIM2 = 100
# Minimum value in the 2nd dimension (double)
MIN_SCALE_DIM2 = 0.1
# Maximum value in the 2nd dimension (double)
MAX_SCALE_DIM2 = 10.

PI_MAX = 50.0

# Output path/file of the data-data pair counted
DD_file = DD.dat
# Output path/file of the random-random pair counted
RR_file = RR.dat
# Output path/file of the data-random pair counted
DR_file = DR.dat
# Output path/file of the correlation function computed
Correlation_function_file = xi.dat

# Enable/disable data-data pair count [true, false]
Compute_DD = true
# Enable/disable random-random pair count [true, false]
Compute_RR = true
# Enable/disable data-random pair count [true, false]
Compute_DR = true

# Use object weights [true, false]
Weight = false




# ---------------------------------------------------------------------------- #
#                   COSMOLOGICAL PARAMETERS FOR Z CONVERSION
# ---------------------------------------------------------------------------- #
# Parameters used to transform redshift to comoving distance

# Matter energy density parameter
om_matter = 0.237

# Radiation energy density parameter
om_radiation = 0.0

# Baryon energy density parameter
om_baryons = 0.041

# Vacuum energy density parameter
om_vac = 0.763

# Curvature energy density parameter
om_k = 0.0

# Hubble parameter in units of 100 Km /s /(Mpc /h)
Hubble = 100.0

# Dimensionless Hubble parameter
hubble = 0.7

# Scalar index of primordial perturbations
spectral_index = 0.9

# Dark energy equation of state
w_eos = -1.0

# Effective number of relativistic species
N_eff = 3.03

# Mass dipersion on sphers of 8 Mpc/h radius
sigma8 = 0.773

# CMB temperature
Tcmb = 2.73
# ---------------------------------------------------------------------------- #
