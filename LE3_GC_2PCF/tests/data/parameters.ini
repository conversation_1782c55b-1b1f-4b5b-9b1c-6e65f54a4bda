# ==============================================================================
#
#  Basic parameter file for
#      - the 2 point correlation function
#
# This parameter file has the following format:
# [Section.Subsection]
#    parameter_name = parameter_value
#
# Lines starting with # are considered comments and ignored
#
# LE3GC Version >= 1.0.0
#
# =========================================================


# ---------------------------------------------------------------------------- #
#                                     PATH
# ---------------------------------------------------------------------------- #
[Path]
  # path to input directory relative to workdir
  input  = LE3_GC_2PCF/tests/data
  # path to output directory relative to workdir
  output = LE3_GC_2PCF/tests/data


# ---------------------------------------------------------------------------- #
#                        PARAMETERS FOR CATALOGS
# ---------------------------------------------------------------------------- #
[Catalog]
  # catalog informations
  constant_depth = true
  nbar_tabulated = true

[Catalog.Galaxy]
  # input file for galaxy catalog with path relative to input_path
  filename = galaxy_coordinates.dat

  # name of HDU in FITS (if FITS input used)
  name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = CARTESIAN

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 999

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 999


[Catalog.Random]
  # input file for random catalog with path relative to input_path
  filename = random_coordinates.dat

  # name of HDU in FITS (if FITS input used)
  name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = CARTESIAN

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 999

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 999

[Catalog.Reconstructed]
  # input file for galaxy catalog with path relative to input_path
  filename = galaxy_coordinates.dat

  # name of HDU in FITS (if FITS input used)
  name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = CARTESIAN

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 999

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 999

[Catalog2]
  # catalog informations
  constant_depth = true
  nbar_tabulated = true

[Catalog2.Galaxy]
  # input file for galaxy catalog with path relative to input_path
  filename = galaxy_coordinates.dat

  # name of HDU in FITS (if FITS input used)
  name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = CARTESIAN

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 999

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 999


[Catalog2.Random]
  # input file for random catalog with path relative to input_path
  filename = random_coordinates.dat

  # name of HDU in FITS (if FITS input used)
  name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = CARTESIAN

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 999

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 999

[Catalog2.Reconstructed]
  # input file for galaxy catalog with path relative to input_path
  filename = galaxy_coordinates.dat

  # name of HDU in FITS (if FITS input used)
  name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = CARTESIAN

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = 999

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = 5

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = 999

#------------------------------------------------------------------------------#
#             PARAMETERS FOR THE 2PT CORRELATION FUNCTION
#------------------------------------------------------------------------------#
[2PCF]
  # Correlation type [AUTO_1D, AUTO_2DCART, AUTO_2DPOL]
  statistics = CROSS_REC_2DPOL
  # Pair counting method [LINKED_LIST, KD_TREE]
  method     = KD_TREE

  # Type of binning along 1st dimension [LIN, LOG]
  bin1_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin1_num   = 100
  # Minimum value in the 1st dimension (double)
  bin1_min   = 0.1
  # Maximum value in the 1st dimension (double)
  bin1_max   = 10.

  # Type of binning along 2nd dimension [LINEAR, LOG]
  bin2_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin2_num   = 100
  # Minimum value in the 1st dimension (double)
  bin2_min   = 0.1
  # Maximum value in the 1st dimension (double)
  bin2_max   = 10.

  PI_MAX = 50.0

  # prefix name for output files
  output_prefix = 

  compute_DD = true
  compute_DR = true
  compute_RR = true

  # Input pairs FITS file (with path relative to workdir) if skip DD calculation
  input_pairs = 

  # Use object weights [true, false]
  use_weight = false

  # enable legacy TXT output files
  ASCII_out = true

  # Split mode: 1=split off
  split_factor = 3

  # Rescale grid
  grid_scale = 1


# ---------------------------------------------------------------------------- #
#                   COSMOLOGICAL PARAMETERS FOR Z CONVERSION
# ---------------------------------------------------------------------------- #
# Parameters used to transform redshift to comoving distance

[Cosmology]

  # ID to identify the cosmological model assumed
  cosmology_ID = LCDM

  # Matter energy density parameter
  om_matter = 0.237

  # Radiation energy density parameter
  om_radiation = 0.0
 
  # Baryon energy density parameter
  om_baryons = 0.041

  # Vacuum energy density parameter
  om_vac = 0.763

  # Curvature energy density parameter
  om_k = 0.0

  # Hubble parameter in units of 100 Km /s /(Mpc /h)
  Hubble = 100.0

  # Dimensionless Hubble parameter
  hubble = 0.7

  # Scalar index of primordial perturbations
  spectral_index = 0.9

  # Dark energy equation of state
  w_eos = -1.0

  # Effective number of relativistic species
  N_eff = 3.03

  # Mass dipersion on sphers of 8 Mpc/h radius
  sigma8 = 0.773

  # CMB temperature
  Tcmb = 2.73
# ---------------------------------------------------------------------------- #
