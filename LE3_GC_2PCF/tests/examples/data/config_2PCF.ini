# ==============================================================================
#
# EUCLID LE3_GC SC8 Catalog configuration
#
# This configuration file defines the structure of the EUCLID catalog FITS files
# used by LE3 Galaxy Clustering Processing Functions in SC8 produced by SEL-ID
#
# SEL-ID DM > 8.0.5 configuration
# =========================================================

[Catalog]
  # catalogs common information
  # unused
  constant_depth = true
  # unused
  nbar_tabulated = false


  [Catalog.Galaxy]
    filename    = no-file-name
    # FITS table name
    name        = CATALOG
    # FITS header keyword identifying the coordinate type
    coordinates = COORD
    # FITS header keyword identifying the angle units
    angle_units = ANGLE
    # FITS header keyword identifying selection
    selection   = SELECT
    # FITS column names for coordinates
    coord1      = RIGHT_ASCENSION
    coord2      = DECLINATION
    coord3      = SPE_Z
    # FITS column name for density
    density     = None
    # FITS column name for weight
    weight      = WEIGHT
    # FITS column name for mask
    mask        = None

  [Catalog.Random]
    filename    = no-file-name
    # FITS table name
    name        = CATALOG
    # FITS header keyword identifying the coordinate type
    coordinates = COORD
    # FITS header keyword identifying the angle units
    angle_units = ANGLE
    # FITS header keyword identifying selection
    selection   = SELECT
    # FITS column names for coordinates
    coord1      = RIGHT_ASCENSION
    coord2      = DECLINATION
    coord3      = SPE_Z
    # FITS column name for density
    density     = None
    # FITS column name for weight
    weight      = WEIGHT
    # FITS column name for mask
    mask        = None
# ==============================================================================
#
#  Basic parameter file for the 2-point correlation function
#
# This parameter file has the following format:
# [Section.Subsection]
#    parameter_name = parameter_value
#
# Lines starting with # are considered comments and ignored
#
# LE3GC Version >= 5.0.0
#
# =========================================================
#------------------------------------------------------------------------------#
#             PARAMETERS FOR THE 2PT CORRELATION FUNCTION
#------------------------------------------------------------------------------#
[2PCF]
  # Correlation type [AUTO_1D, AUTO_2DCART, AUTO_2DPOL]
  statistics = AUTO_2DPOL
  # Pair counting method [LINKED_LIST, KD_TREE, OCTREE]
  method     = LINKED_LIST

  # Type of binning along 1st dimension [LIN, LOG]
  bin1_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin1_num   = 20
  # Minimum value in the 1st dimension (double)
  bin1_min   = 4.
  # Maximum value in the 1st dimension (double)
  bin1_max   = 100.

  # Type of binning along 2nd dimension [LINEAR, LOG]
  bin2_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin2_num   = 20
  # Minimum value in the 1st dimension (double)
  bin2_min   = 4.
  # Maximum value in the 1st dimension (double)
  bin2_max   = 100.

  PI_MAX = 50.0

  # prefix name for output files
  output_prefix = 

  compute_DD = true
  compute_DR = true
  compute_RR = true

  # Input pairs FITS file (with path relative to workdir) if skip DD calculation
  input_pairs = 

  # Use object weights [true, false]
  use_weight = false

  # enable legacy TXT output files
  ASCII_out = false

  # Split mode: 1=split off
  split_factor = 50

  # Rescale grid
  grid_scale = 1
# ---------------------------------------------------------------------------- #
#                   COSMOLOGICAL PARAMETERS FOR Z CONVERSION
# ---------------------------------------------------------------------------- #
# Parameters used to transform redshift to comoving distance
# d = Hubble*sqrt(om_matter*(1.0 + z)^3 + om_radiation*(1.0 + z)^4 +
#                 om_k*(1.0 + z)^2 + om_vac*(1.0 + z)^[3*(1 + w_eos)]) [1/h Mpc]


[Cosmology]

    cosmology_ID = default

    # Matter energy density parameter
    om_matter = 0.237

    # Vacuum energy density parameter
    om_vac = 0.763

    # Dark energy equation of state
    w_eos = -1.0

    # Radiation energy density parameter
    om_radiation = 0.0

    # Curvature energy density parameter
    om_k = 0.0

    # Hubble parameter in units of 100 km/s /(Mpc /h)
    # which must be 100 by definition
    Hubble = 100.0


    # Following cosmological paramters are not used for redshift -> distance

    # Baryon energy density parameter
    om_baryons = 0.041

    # Dimensionless Hubble parameter
    hubble = 0.7

    # Scalar index of primordial perturbations
    spectral_index = 0.9

    # Effective number of relativistic species
    N_eff = 3.03

    # Mass dipersion on sphers of 8 Mpc/h radius
    sigma8 = 0.773

    # CMB temperature
    Tcmb = 2.7355
# ---------------------------------------------------------------------------- #
