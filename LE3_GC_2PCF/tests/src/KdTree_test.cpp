/**
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      tests/src/KdTree_test.cpp
 * @date      01/01/18
 * <AUTHOR> <tavagna<PERSON>@oats.inaf.it> [integration SDC-IT]
 * <AUTHOR> <<EMAIL>> [unit testing]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License 
 */

#define BOOST_TEST_MODULE KdTree_test

#include <boost/test/unit_test.hpp>
#include <boost/filesystem.hpp>
#include <fstream>
#include <string>
#include <vector>

#include "LE3_GC_Libraries/Fits.h"
#include "LE3_GC_Libraries/Cosmology.h"
#include "LE3_GC_Libraries/Galaxy.h"

#define private public
#define protected public
#include "LE3_GC_2PCF/KdTree.h"
#include "LE3_GC_Libraries/CatalogAscii.h"
#include "LE3_GC_Libraries/Parameters.h"

namespace fs = boost::filesystem;

using le3gc::LE3_GC_2PCF::KdTree;
using le3gc::LE3_GC_2PCF::Node;
using le3gc::logger;
using le3gc::LE3_GC_2PCF::CorrelationType;
using le3gc::BinningType;
using le3gc::LE3_GC_2PCF::ScalesType;
using le3gc::LE3_GC_2PCF::BoxType;
using le3gc::CatalogAscii;
using le3gc::Fits;
using le3gc::Cosmology;
using le3gc::Galaxy;
using le3gc::Parameters;
using le3gc::CatalogAscii;
using le3gc::LE3_GC_2PCF::InputCatType;

using std::string;
using std::vector;
using std::fstream;
using std::endl;
using std::shared_ptr;
using std::make_shared;

// Fixed Cosmology
Cosmology cosmo("", 0.237, 0.0, 0.041, 0.763, 0.0, 100.0, 0.9, -1.0, 3.03, 0.773, 2.73);

// fixture for unit tests
struct KdTreeEnv {

  KdTree tpcf;
  Parameters params;
  CatalogAscii datacat;
  CatalogAscii randcat;

  // reference products
  string refCorr1Dname = "refCorr1D.txt";
  string refCorr2Dname = "refCorr2D.txt";
  string refPair1Dname = "refPair1D.txt";
  string refPair2Dname = "refPair2D.txt";
  string refCrossPair1Dname = "refCrossPair1D.txt";
  string refCrossPair2Dname = "refCrossPair2D.txt";

  // path to reference test files
  string basePath;
  string tmpFitsName;
  string tmpFits2Name;
  string tmpTxtName;
  string tmpTxt2Name;

  // configurations
  string typeNONE = "NONE";
  string type1D = "AUTO_1D";
  string type2Dc = "AUTO_2DCART";
  string type2Dp = "AUTO_2DPOL";
  string typeC1D = "CROSS_1D";
  string typeC2Dc = "CROSS_2DCART";
  string typeC2Dp = "CROSS_2DPOL";
  string typeLIN = "LIN";
  string typeLOG = "LOG";

  double min1 = 1.;
  double min2 = 0.1;
  double max1 = 11.;
  double max2 = 1000.;
  int n1   = 100;
  int n2   = 5;

  // constructor
  KdTreeEnv() {
    // workdir
    basePath = getenv("LE3_GC_TWOPOINTCORRELATION_PROJECT_ROOT") + static_cast<string>("/LE3_GC_2PCF/tests/data/");

    // redefine adding path
    refCorr1Dname = basePath + refCorr1Dname;
    refCorr2Dname = basePath + refCorr2Dname;
    refPair1Dname = basePath + refPair1Dname;
    refPair2Dname = basePath + refPair2Dname;
    refCrossPair1Dname = basePath + refCrossPair1Dname;
    refCrossPair2Dname = basePath + refCrossPair2Dname;

    // path for test local data
    tmpFitsName = fs::unique_path("test_KDTREE.fits").native();

    // path for test local data
    tmpFits2Name = fs::unique_path("test_KDTREE_aux.fits").native();

    // path for test local data TXT
    tmpTxtName = fs::unique_path("test_KDTREE.txt").native();

    // path for test local data TXT
    tmpTxt2Name = fs::unique_path("test_KDTREE_aux.txt").native();
  }

  void build2PFC(const string& confname) {
    Parameters localParams = setupParameters(confname);
    tpcf = KdTree(localParams);
  }

  // setup input configuration file
  Parameters setupParameters (const string& confname) {

    // open file
    fstream tmpfile(tmpTxtName, std::ios::out);
    tmpfile << "[2PCF]" << endl;

    // specific configuration
    string name = "NULL";
    if ("NONE" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeNONE << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = UNUSED" << endl;
      tmpfile << "bin2_num   = UNUSED" << endl;
      tmpfile << "bin2_min   = UNUSED" << endl;
      tmpfile << "bin2_max   = UNUSED" << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("AUTO_1D" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << type1D << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type = UNUSED" << endl;
      tmpfile << "bin2_num  = UNUSED" << endl;
      tmpfile << "bin2_min  = UNUSED" << endl;
      tmpfile << "bin2_max  = UNUSED" << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("AUTO_2DCART" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << type2Dc << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = " << max2 << endl;

    } else if ("AUTO_2DPOL" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << type2Dp << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("CROSS_1D" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeC1D << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type = UNUSED" << endl;
      tmpfile << "bin2_num  = UNUSED" << endl;
      tmpfile << "bin2_min  = UNUSED" << endl;
      tmpfile << "bin2_max  = UNUSED" << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("CROSS_2DCART" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeC2Dc << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = " << max2 << endl;

    } else if ("CROSS_2DPOL" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeC2Dp << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    }
    
    tmpfile << "use_weight = true" << endl;
    tmpfile << "ASCII_out = false" << endl;
    tmpfile << "split_factor = 1" << endl;
    tmpfile.close();

    return Parameters(tmpTxtName);
  }


  // load a basic catalog with 103 elements as test DATA catalog
  void loadTestDataCat() {

    // build parameter file for catalog configuration
    fstream tmpfile(tmpTxtName, std::ios::out);
    tmpfile << "[Catalog]" << endl;
    tmpfile << "constant_depth = true" << endl;
    tmpfile << "nbar_tabulated = true" << endl;
    tmpfile << "[Catalog.Galaxy]" << endl;
    tmpfile << "name = " << tmpTxtName << endl;
    tmpfile << "coordinates = CARTESIAN" << endl;
    tmpfile << "angle_units = DEG" << endl;
    tmpfile << "coord1 = 1" << endl;
    tmpfile << "coord2 = 2" << endl;
    tmpfile << "coord3 = 3" << endl;
    tmpfile << "density = 4" << endl;
    tmpfile << "weight = 5" << endl;
    tmpfile << "mask = 6" << endl;
    tmpfile.clear();
    tmpfile.close();

    // configure catalog
    Parameters par(tmpTxtName);
    datacat = CatalogAscii(le3gc::CatalogType::DATA);
    datacat.configure(par, cosmo);

    // build catalog ASCII file
    tmpfile.open(tmpTxtName, std::ios::out);
    tmpfile << "-3.0   -2.0   -1.0    2.0   1.0" << endl; // min pos
    tmpfile << " 1.0    2.0    3.0    5.0   1.0" << endl; // max pos
    tmpfile << "   0      0      0      0     0" << endl; // empty
    for (int i=0; i<100; ++i)
      tmpfile << "1.0    1.0    1.0    1.0    1.0" << endl; // other 100 lines to test SetupPairCount() 
    tmpfile.close();

    // load catalog
    datacat.openFile(tmpTxtName);
    datacat.loadFromFile();
  }

  // load a basic catalog with 104 elements as test RANDOM catalog
  void loadTestRandCat() {

    fstream tmpfile(tmpTxt2Name, std::ios::out);
    tmpfile << "[Catalog]" << endl;
    tmpfile << "constant_depth = true" << endl;
    tmpfile << "nbar_tabulated = true" << endl;
    tmpfile << "[Catalog.Random]" << endl;
    tmpfile << "name = " << tmpTxt2Name << endl;
    tmpfile << "coordinates = CARTESIAN" << endl;
    tmpfile << "angle_units = DEG" << endl;
    tmpfile << "coord1 = 1" << endl;
    tmpfile << "coord2 = 2" << endl;
    tmpfile << "coord3 = 3" << endl;
    tmpfile << "density = 4" << endl;
    tmpfile << "weight = 5" << endl;
    tmpfile << "mask = 6" << endl;
    tmpfile.clear();
    tmpfile.close();

    // configure catalog
    Parameters par(tmpTxt2Name);
    randcat = CatalogAscii(le3gc::CatalogType::RAND);
    randcat.configure(par, cosmo);

    // rewrite file with catalog data
    tmpfile.open(tmpTxt2Name, std::ios::out);
    tmpfile << "-3.0   -2.0   -1.0    2.0   1.0" << endl; // min pos
    tmpfile << " 1.0    2.0    3.0    5.0   1.0" << endl; // max pos
    tmpfile << "   0      0      0      0     0" << endl; // empty
    tmpfile << "-5.0   -1.0   -3.0    2.0   1.0" << endl; // new min2 on x,z
    for (int i=0; i<100; ++i)
      tmpfile << "1.0    1.0    1.0    1.0    1.0" << endl; // other 100 lines to test SetupPairCount() 
    tmpfile.close();

    // load catalog
    randcat.openFile(tmpTxt2Name);
    randcat.loadFromFile();
  }

  void clean() {
    if (true == fs::exists(tmpFitsName)) {
        fs::remove(tmpFitsName);
    }
    if (true == fs::exists(tmpFits2Name)) {
        fs::remove(tmpFits2Name);
    }
    if (true == fs::exists(tmpTxtName)) {
        fs::remove(tmpTxtName);
    }
    if (true == fs::exists(tmpTxt2Name)) {
        fs::remove(tmpTxt2Name);
    }
  }

  ~KdTreeEnv() {
    clean();
  };

} e;

//-----------------------------------------------------------------------------

BOOST_AUTO_TEST_SUITE (KdTree_test)

//-----------------------------------------------------------------------------

// test the default empty constructor: no modification on global object
BOOST_AUTO_TEST_CASE(constructor_test) {

  logger.info();
  logger.info() << "---> KDTREE: empty constructor";
    
  // empty object
  BOOST_REQUIRE_NO_THROW(KdTree l);
  KdTree l;

  // test empty scales
  BOOST_REQUIRE_CLOSE(l.m_scales.min1Dsq,     0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1Dsq,     0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min1D,       0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1D,       0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2Dsq,     0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2Dsq,     0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2D,       0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2D,       0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.dim1_binInv, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.dim2_binInv, 0.0, 0.00001);    

  // test box ?

  // test scales and statistics
  BOOST_CHECK(l.m_scale1D == BinningType::NONE);
  BOOST_CHECK(l.m_scale2D == BinningType::NONE);
  BOOST_CHECK(l.m_statistics == CorrelationType::NONE);
    
  // test binning 1D
  BOOST_CHECK_EQUAL(l.m_nBin1D, 0);
  BOOST_REQUIRE_CLOSE(l.m_min1D, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max1D, 0.0, 0.00001);
    
  // test binning 2D
  BOOST_CHECK_EQUAL(l.m_nBin2D, 0);
  BOOST_REQUIRE_CLOSE(l.m_min2D, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max2D, 0.0, 0.00001);

  // test maximum pi scale for the integral in the projected statistics
  BOOST_REQUIRE_CLOSE(l.m_piMax, 0.0, 0.00001);

  // test that no weighting scheme is applied 
  BOOST_CHECK(false == l.m_useWeight);

  // test that automatic splitting is applited
  BOOST_CHECK_EQUAL(l.m_nsplit, 0);

  // test that no txt file is produced in output
  BOOST_CHECK(false == l.m_useTXT);

  // test that no counting method is set
  BOOST_CHECK(l.m_method == "");

}


// -------------------------------------------------------------------------- //


// test the default parameter constructor
BOOST_AUTO_TEST_CASE(constructor_parameters_test) {

  logger.info();
  logger.info() << "---> KDTREE: default parameter constructor";
  
  // default perameters: NO statistics --> default AUTO_1D
  Parameters params = e.setupParameters("NONE");
  
  // object from parameter file
  KdTree l(params);
  
  // test empty scales
  BOOST_REQUIRE_CLOSE(l.m_scales.min1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2Dsq, 0.0, 0.00001);

  // test box ?

  // test scalesa and statistics
  BOOST_CHECK(l.m_scale1D == BinningType::LIN);
  BOOST_CHECK(l.m_scale2D == BinningType::NONE);
  BOOST_CHECK(l.m_statistics == CorrelationType::NONE);
  
  // test binning 1D
  BOOST_CHECK_EQUAL(l.m_nBin1D, e.n1);
  BOOST_REQUIRE_CLOSE(l.m_min1D, e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max1D, e.max1, 0.00001);
  
  // test binning 2D
  BOOST_CHECK_EQUAL(l.m_nBin2D, 0);
  BOOST_REQUIRE_CLOSE(l.m_min2D, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max2D, 0.0, 0.00001);

  // test maximum pi scale for the integral in the projected statistics
  BOOST_REQUIRE_CLOSE(l.m_piMax, 0.0, 0.00001);

  // test the weights 
  BOOST_CHECK(true == l.m_useWeight);

  // test that no txt file is produced in output
  BOOST_CHECK(false == l.m_useTXT);

  // test that no counting method is set
  BOOST_CHECK(l.m_method == "KD_TREE");

}


// -------------------------------------------------------------------------- //


// test the parameter constructor for 1D output product
BOOST_AUTO_TEST_CASE(constructor_parameters1D_test) {

  logger.info();
  logger.info() << "---> KDTREE: parameter 1D constructor";

  // default parameters AUTO_1D
  Parameters params = e.setupParameters("AUTO_1D");

  // object from parameter file
  KdTree l(params);

  // test empty scales
  BOOST_REQUIRE_CLOSE(l.m_scales.min1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2Dsq, 0.0, 0.00001);

  // test box ?

  // test scalesa and statistics
  BOOST_CHECK(l.m_scale1D == BinningType::LIN);
  BOOST_CHECK(l.m_scale2D == BinningType::NONE);
  BOOST_CHECK(l.m_statistics == CorrelationType::AUTO_1D);
  
  // test binning 1D
  BOOST_CHECK_EQUAL(l.m_nBin1D, e.n1);
  BOOST_REQUIRE_CLOSE(l.m_min1D, e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max1D, e.max1, 0.00001);
  
  // test binning 2D
  BOOST_CHECK_EQUAL(l.m_nBin2D, 1);
  BOOST_REQUIRE_CLOSE(l.m_min2D, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max2D, 0.0, 0.00001);

  // test maximum pi scale for the integral in the projected statistics
  BOOST_REQUIRE_CLOSE(l.m_piMax, 0.0, 0.00001);

  // test the weights 
  BOOST_CHECK(true == l.m_useWeight);

  // test that no txt file is produced in output
  BOOST_CHECK(false == l.m_useTXT);

  // test that no counting method is set
  BOOST_CHECK(l.m_method == "KD_TREE");

}


// -------------------------------------------------------------------------- //


// test the parameter constructor for 2D Cartesian output product
BOOST_AUTO_TEST_CASE(constructor_parameters2DCart_test) {

  logger.info();
  logger.info() << "---> KDTREE: parameters 2D Cartesian constructor";
  
  // default parameters AUTO_2DCART
  Parameters params = e.setupParameters("AUTO_2DCART");

  // object from parameter file
  KdTree l(params);

  // test empty scales
  BOOST_REQUIRE_CLOSE(l.m_scales.min1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2Dsq, 0.0, 0.00001);
  
  // test box ?
  
  // test scalesa and statistics
  BOOST_CHECK(l.m_scale1D == BinningType::LIN);
  BOOST_CHECK(l.m_scale2D == BinningType::LOG);
  BOOST_CHECK(l.m_statistics == CorrelationType::AUTO_2DCART);

  // test binning 1D
  BOOST_CHECK_EQUAL(l.m_nBin1D, e.n1);
  BOOST_REQUIRE_CLOSE(l.m_min1D, e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max1D, e.max1, 0.00001);

  // test binning 2D
  BOOST_CHECK_EQUAL(l.m_nBin2D, e.n2);
  BOOST_REQUIRE_CLOSE(l.m_min2D, e.min2, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max2D, e.max2, 0.00001);

  // test maximum pi scale for the integral in the projected statistics
  BOOST_REQUIRE_CLOSE(l.m_piMax, e.max2, 0.00001);

  // test the weights 
  BOOST_CHECK(true == l.m_useWeight);

  // test that no txt file is produced in output
  BOOST_CHECK(false == l.m_useTXT);

  // test that no counting method is set
  BOOST_CHECK(l.m_method == "KD_TREE");

}


// -------------------------------------------------------------------------- //


// test parameter constructor for 2D polar output product
BOOST_AUTO_TEST_CASE(constructor_parameters2Dpol_test) {

  logger.info();
  logger.info() << "---> KDTREE: parameters 2D polar constructor";
  
  // default perameters AUTO_2DCart
  Parameters params = e.setupParameters("AUTO_2DPOL");

  // object from parameter file
  KdTree l(params);

  // test empty scales
  BOOST_REQUIRE_CLOSE(l.m_scales.min1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max1Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2D,   0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.min2Dsq, 0.0, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_scales.max2Dsq, 0.0, 0.00001);
  
  // test box ?
  
  // test scalesa and statistics
  BOOST_CHECK(l.m_scale1D == BinningType::LIN);
  BOOST_CHECK(l.m_scale2D == BinningType::LIN);
  BOOST_CHECK(l.m_statistics == CorrelationType::AUTO_2DPOL);

  // test binning 1D
  BOOST_CHECK_EQUAL(l.m_nBin1D, e.n1);
  BOOST_REQUIRE_CLOSE(l.m_min1D, e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max1D, e.max1, 0.00001);

  // test binning 2D
  BOOST_CHECK_EQUAL(l.m_nBin2D, e.n2);
  BOOST_REQUIRE_CLOSE(l.m_min2D, -1, 0.00001);
  BOOST_REQUIRE_CLOSE(l.m_max2D,  1, 0.00001);

  // test maximum pi scale for the integral in the projected statistics
  BOOST_REQUIRE_CLOSE(l.m_piMax, 0.0, 0.00001);

  // test the weights 
  BOOST_CHECK(true == l.m_useWeight);

  // test that no txt file is produced in output
  BOOST_CHECK(false == l.m_useTXT);

  // test that no counting method is set
  BOOST_CHECK(l.m_method == "KD_TREE");

}

// -------------------------------------------------------------------------- //

// test pairs setup
BOOST_AUTO_TEST_CASE(setup_pairs_auto1D_test) {

  logger.info();
  logger.info() << "--> KDTREE: setup pairs AUTO_1D";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // prepare the 2PCF measure with catalog
  e.tpcf.SetupPairCount();

  // check if minimum and maximum scales are correct
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1Dsq, pow(e.min1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1Dsq, pow(e.max1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1D,  e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1D,  e.max1, 0.00001);

  // check m_box parameter values
  for (size_t i=0; i<4; ++i) {
    BOOST_CHECK(e.tpcf.m_box.posMin[i] == 0);
    BOOST_CHECK(e.tpcf.m_box.posMax[i] == 0);
  }
  for (size_t i=0; i<4; ++i) 
    BOOST_CHECK(e.tpcf.m_box.nCell[i] == 0);

  BOOST_CHECK(e.tpcf.m_box.defaultBoxCell == 0.);
  BOOST_CHECK(e.tpcf.m_box.maxScale == 0.);
  BOOST_CHECK(e.tpcf.m_box.nobjects == 0);
  BOOST_CHECK(e.tpcf.m_box.set_from_params == 0);
}

// -------------------------------------------------------------------------- //


// test the data catalog setup
BOOST_AUTO_TEST_CASE(setup_pairs_cross1D_test) {

  logger.info();
  logger.info() << "---> KDTREE: SetupPairCount with CROSS_1D";

  // build the 1D cross-correlation
  e.build2PFC("CROSS_1D");

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // check if minimum and maximum scales are correct
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1Dsq, pow(e.min1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1Dsq, pow(e.max1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1D,  e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1D,  e.max1, 0.00001);

  // check m_box parameter values
  for (size_t i=0; i<4; ++i) {
    BOOST_CHECK(e.tpcf.m_box.posMin[i] == 0);
    BOOST_CHECK(e.tpcf.m_box.posMax[i] == 0);
  }
  for (size_t i=0; i<4; ++i) 
    BOOST_CHECK(e.tpcf.m_box.nCell[i] == 0);

  BOOST_CHECK(e.tpcf.m_box.defaultBoxCell == 0.);
  BOOST_CHECK(e.tpcf.m_box.maxScale == 0.);
  BOOST_CHECK(e.tpcf.m_box.nobjects == 0);
  BOOST_CHECK(e.tpcf.m_box.set_from_params == 0);
}

// -------------------------------------------------------------------------- //


// test the data catalog setup
BOOST_AUTO_TEST_CASE(setup_pairs_auto2DCart_test) {

  logger.info();
  logger.info() << "---> KDTREE: SetupPairCount with AUTO_2DCART";

  // build the 2D Cartesian auto-correlation
  e.build2PFC("AUTO_2DCART");

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // check if minimum and maximum scales are correct
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1Dsq, pow(e.min1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1Dsq, pow(e.max1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1D, e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1D, e.max1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2Dsq, pow(e.min2, 2.), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2Dsq, pow(e.max2, 2.), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2D, log10(e.min2), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2D, log10(e.max2), 0.00001);

  // check m_box parameter values
  for (size_t i=0; i<4; ++i) {
    BOOST_CHECK(e.tpcf.m_box.posMin[i] == 0);
    BOOST_CHECK(e.tpcf.m_box.posMax[i] == 0);
  }
  for (size_t i=0; i<4; ++i) 
    BOOST_CHECK(e.tpcf.m_box.nCell[i] == 0);

  BOOST_REQUIRE_CLOSE(e.tpcf.m_box.defaultBoxCell, 0., 0.001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_box.maxScale, 0., 0.001);
  BOOST_CHECK(e.tpcf.m_box.nobjects == 0);
  BOOST_CHECK(e.tpcf.m_box.set_from_params == 0);
}

// -------------------------------------------------------------------------- //


// test the data catalog setup
BOOST_AUTO_TEST_CASE(setup_pairs_cross2DCart_test) {

  logger.info();
  logger.info() << "---> KDTREE: SetupPairCount with CROSS_2DCART";

  // build the 2D Cartesian cross-correlation
  e.build2PFC("CROSS_2DCART");

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // check if minimum and maximum scales are correct
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1Dsq, pow(e.min1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1Dsq, pow(e.max1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1D, e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1D, e.max1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2Dsq, pow(e.min2, 2.), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2Dsq, pow(e.max2, 2.), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2D, log10(e.min2), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2D, log10(e.max2), 0.00001);

  // check m_box parameter values
  for (size_t i=0; i<4; ++i) {
    BOOST_CHECK(e.tpcf.m_box.posMin[i] == 0);
    BOOST_CHECK(e.tpcf.m_box.posMax[i] == 0);
  }
  for (size_t i=0; i<4; ++i) 
    BOOST_CHECK(e.tpcf.m_box.nCell[i] == 0);

  BOOST_REQUIRE_CLOSE(e.tpcf.m_box.defaultBoxCell, 0., 0.001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_box.maxScale, 0., 0.001);
  BOOST_CHECK(e.tpcf.m_box.nobjects == 0);
  BOOST_CHECK(e.tpcf.m_box.set_from_params == 0);
}

// -------------------------------------------------------------------------- //


// test the data catalog setup
BOOST_AUTO_TEST_CASE(setup_pairs_auto2DPol_test) {

  logger.info();
  logger.info() << "---> KDTREE: SetupPairCount with AUTO_2DPOL";

  // build the 2D polar auto-correlation
  e.build2PFC("AUTO_2DPOL");

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // check if minimum and maximum scales are correct
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1Dsq, pow(e.min1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1Dsq, pow(e.max1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1D,  e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1D,  e.max1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2Dsq, 1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2Dsq, 1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2D, -1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2D,  1, 0.00001);

  // check m_box parameter values
  for (size_t i=0; i<4; ++i) {
    BOOST_CHECK(e.tpcf.m_box.posMin[i] == 0);
    BOOST_CHECK(e.tpcf.m_box.posMax[i] == 0);
  }
  for (size_t i=0; i<4; ++i) 
    BOOST_CHECK(e.tpcf.m_box.nCell[i] == 0);
 
  BOOST_CHECK(e.tpcf.m_box.defaultBoxCell == 0.);
  BOOST_CHECK(e.tpcf.m_box.maxScale == 0.);
  BOOST_CHECK(e.tpcf.m_box.nobjects == 0);
  BOOST_CHECK(e.tpcf.m_box.set_from_params == 0);
}

// -------------------------------------------------------------------------- //


// test the data catalog setup
BOOST_AUTO_TEST_CASE(setup_pairs_cross2DPol_test) {

  logger.info();
  logger.info() << "---> KDTREE: SetupPairCount with CROSS_2DPOL";

  // build the 2D polar cross-correlation
  e.build2PFC("CROSS_2DPOL");

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // check if minimum and maximum scales are correct
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1Dsq, pow(e.min1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1Dsq, pow(e.max1, 2.0), 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min1D,  e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max1D,  e.max1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2Dsq, 1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2Dsq, 1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.min2D, -1, 0.00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_scales.max2D,  1, 0.00001);

  // check m_box parameter values
  for (size_t i=0; i<4; ++i) {
    BOOST_CHECK(e.tpcf.m_box.posMin[i] == 0);
    BOOST_CHECK(e.tpcf.m_box.posMax[i] == 0);
  }
  for (size_t i=0; i<4; ++i) 
    BOOST_CHECK(e.tpcf.m_box.nCell[i] == 0);

  BOOST_CHECK(e.tpcf.m_box.defaultBoxCell == 0.);
  BOOST_CHECK(e.tpcf.m_box.maxScale == 0.);
  BOOST_CHECK(e.tpcf.m_box.nobjects == 0);
  BOOST_CHECK(e.tpcf.m_box.set_from_params == 0);
}

// -------------------------------------------------------------------------- //


// test the creation of the kd-tree
BOOST_AUTO_TEST_CASE(indicize_data_test) {

  logger.info();
  logger.info() << "--> KDTREE: create data tree";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // load the test data catalog
  e.loadTestDataCat();

  // prepare the 2PCF measure with catalog
  e.tpcf.SetupPairCount();

  // create the kd-tree structure
  e.tpcf.Indicize(e.datacat, InputCatType::DATA);

  // box setted
  BOOST_CHECK(e.tpcf.m_box.posMin[0] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMin[1] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMin[2] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMax[0] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMax[1] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMax[2] == 0);

  // tree creation
  //BOOST_CHECK(e.tpcf.m_treeData1->first == 0);
  //BOOST_CHECK(e.tpcf.m_treeData1->last == 103);
  //BOOST_CHECK(e.tpcf.m_treeData1->hasChildren == true);

  e.clean();
}

// -------------------------------------------------------------------------- //

// test the creation of the kd-tree
BOOST_AUTO_TEST_CASE(indicize_random_test) {

  logger.info();
  logger.info() << "--> KDTREE: create random tree";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // load the test data catalog
  e.loadTestRandCat();

  // prepare the 2PCF measure with catalog
  e.tpcf.SetupPairCount();

  // create the kd-tree structure
  e.tpcf.Indicize(e.randcat, InputCatType::RAND);

  // box setted
  BOOST_CHECK(e.tpcf.m_box.posMin[0] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMin[1] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMin[2] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMax[0] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMax[1] == 0);
  BOOST_CHECK(e.tpcf.m_box.posMax[2] == 0);

  // tree creation
  //BOOST_CHECK(e.tpcf.m_treeRand1->first == 0);
  //BOOST_CHECK(e.tpcf.m_treeRand1->last == 104);
  //BOOST_CHECK(e.tpcf.m_treeRand1->hasChildren == true);

  e.clean();
}

// -------------------------------------------------------------------------- //


// test the countPairs function
BOOST_AUTO_TEST_CASE(countPairs_DD_test) {

  logger.info();
  logger.info() << "---> KDTREE: test the DD pair counting";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // load the test data catalog
  e.loadTestDataCat();

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // create the linked-list-like structure
  e.tpcf.Indicize(e.datacat, InputCatType::DATA);
  
  // count the DD pairs
  vector<double> pairsDD(0);
  double pairmax = 0;
  e.tpcf.countPairs(e.datacat, e.datacat, InputCatType::DATA, InputCatType::DATA, pairsDD, pairmax, false);

  std::vector<double> expected(e.n1, 0.);
  expected[12] = expected[43] = 100;
  expected[59] = 1;
  BOOST_CHECK_EQUAL_COLLECTIONS(pairsDD.begin(), pairsDD.end(), expected.begin(), expected.end());

  e.clean();
}// -------------------------------------------------------------------------- //


// test the countPairs function
BOOST_AUTO_TEST_CASE(countPairs_DD2_test) {

  logger.info();
  logger.info() << "---> KDTREE: test the DD2 pair counting";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // load the test data catalog
  e.loadTestDataCat();

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // create the linked-list-like structure
  e.tpcf.Indicize(e.datacat, InputCatType::DATA);

  // set use weights
  e.tpcf.m_useWeight = true;
  
  // count the DD pairs
  vector<double> pairsDD(0);
  double pairmax = 0;
  e.tpcf.countPairs(e.datacat, e.datacat, InputCatType::DATA, InputCatType::DATA, pairsDD, pairmax, true);

  std::vector<double> expected(e.n1, 0.);
  expected[12] = expected[43] = 200;
  expected[59] = 2;
  BOOST_CHECK_EQUAL_COLLECTIONS(pairsDD.begin(), pairsDD.end(), expected.begin(), expected.end());

  e.clean();
}

// -------------------------------------------------------------------------- //


// test the countPairs function
BOOST_AUTO_TEST_CASE(countPairs_RR_test) {

  logger.info();
  logger.info() << "---> KDTREE: test the RR pair counting";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // load the test data catalog
  e.loadTestRandCat();

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // create the linked-list-like structure
  e.tpcf.Indicize(e.randcat, InputCatType::RAND);
  
  // count the DD pairs
  vector<double> pairsDD(0);
  double pairmax = 0;
  e.tpcf.countPairs(e.randcat, e.randcat, InputCatType::RAND, InputCatType::RAND, pairsDD, pairmax, false);

  std::vector<double> expected(e.n1, 0.);
  expected[12] = expected[43] = expected[64] = 100;
  expected[59] = expected[20] = expected[80] = 1;
  BOOST_CHECK_EQUAL_COLLECTIONS(pairsDD.begin(), pairsDD.end(), expected.begin(), expected.end());

  e.clean();
}

// test the countPairs function
BOOST_AUTO_TEST_CASE(countPairs_DR_test) {

  logger.info();
  logger.info() << "---> KDTREE: test the DR pair counting";

  // default parameters AUTO_1D
  e.build2PFC("AUTO_1D");

  // load the test data catalog
  e.loadTestDataCat();

  // load the test random catalog
  e.loadTestRandCat();

  // set minimum and maximum scales
  e.tpcf.SetupPairCount();

  // create the linked-list-like structure
  e.tpcf.Indicize(e.datacat, InputCatType::DATA);

  // create the linked-list-like structure
  e.tpcf.Indicize(e.randcat, InputCatType::RAND);
  
  // count the DR pairs
  vector<double> pairsDR(0);
  double pairmax = 0;
  e.tpcf.countPairs(e.datacat, e.randcat, InputCatType::DATA, InputCatType::RAND, pairsDR, pairmax, true);

  std::vector<double> expected(e.n1, 0.);
  expected[64] = 100;
  expected[43] = expected[12] = 200;
  expected[80] = expected[20] = 1;
  expected[59] = 2;
  BOOST_CHECK_EQUAL_COLLECTIONS(pairsDR.begin(), pairsDR.end(), expected.begin(), expected.end());

  e.clean();
}

// -------------------------------------------------------------------------- //
// -------------------------------------------------------------------------- //

BOOST_AUTO_TEST_SUITE_END ()

