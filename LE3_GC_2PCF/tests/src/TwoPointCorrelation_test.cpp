/**
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file tests/src/LE3_GC_ComputeTwoPointCorrelation_test.cpp
 * @date 01/05/18
 * <AUTHOR>
 */

#define BOOST_TEST_MODULE LTwoPointCorrelation_test

#include <boost/test/unit_test.hpp>
#include <boost/filesystem.hpp>
#include <fstream>
#include <string>
#include <vector>

#include "LE3_GC_Libraries/Fits.h"
#include "LE3_GC_Libraries/Cosmology.h"
#include "LE3_GC_Libraries/Galaxy.h"
#include "LE3_GC_Libraries/TypeDef.h"

#define private public
#define protected public
#include "LE3_GC_2PCF/LinkedList.h"
#include "LE3_GC_Libraries/CatalogAscii.h"
#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_2PCF/TwoPointCorrelation.h"
#include "LE3_GC_Libraries/ByendingsDm.h"

namespace fs = boost::filesystem;

using std::string;
using std::vector;
using std::fstream;
using std::endl;
using std::shared_ptr;
using std::make_shared;

using le3gc::logger;
using le3gc::BinningType;
using le3gc::CatalogAscii;
using le3gc::Fits;
using le3gc::Cosmology;
using le3gc::Galaxy;
using le3gc::Parameters;
using le3gc::CatalogAscii;
using le3gc::ByendingsDm;

using le3gc::LE3_GC_2PCF::LinkedList;
using le3gc::LE3_GC_2PCF::CorrelationType;
using le3gc::LE3_GC_2PCF::ScalesType;
using le3gc::LE3_GC_2PCF::BoxType;
using le3gc::LE3_GC_2PCF::TwoPointCorrelation;

// Fixed Cosmology
Cosmology cosmo("", 0.237, 0.0, 0.041, 0.763, 0.0, 100.0, 0.9, -1.0, 3.03, 0.773, 2.73);

// fixture for unit tests
struct LinkedListEnv {

  LinkedList tpcf;
  Parameters params;
  CatalogAscii datacat;
  CatalogAscii randcat;

  // reference products
  string refCorr1Dname = "refCorr1D.txt";
  string refCorr2Dname = "refCorr2D.txt";
  string refPair1Dname = "refPair1D.txt";
  string refPair2Dname = "refPair2D.txt";
  string refCrossPair1Dname = "refCrossPair1D.txt";
  string refCrossPair2Dname = "refCrossPair2D.txt";

  // path to reference test files
  string rootdir;
  string basePath;
  string parFile;
  string dataFile;
  string randFile;

  string tmpFitsName;
  string tmpFits2Name;
  string tmpTxtName;
  string tmpTxt2Name;
  string tmpCorrName;
  string tmpPairName;
  string tmpCorr2Name;

  // configurations
  string typeNONE = "NONE";
  string type1D = "AUTO_1D";
  string type2Dc = "AUTO_2DCART";
  string type2Dp = "AUTO_2DPOL";
  string typeC1D = "CROSS_1D";
  string typeC2Dc = "CROSS_2DCART";
  string typeC2Dp = "CROSS_2DPOL";
  string typeLIN = "LIN";
  string typeLOG = "LOG";

  double min1 = 1.;
  double min2 = 0.1;
  double max1 = 11.;
  double max2 = 1000.;
  int n1   = 100;
  int n2   = 5;

  // constructor
  LinkedListEnv() {
    // rootdir
    rootdir = getenv("LE3_GC_TWOPOINTCORRELATION_PROJECT_ROOT");

    // workdir
    basePath = getenv("LE3_GC_TWOPOINTCORRELATION_PROJECT_ROOT") + static_cast<string>("/LE3_GC_2PCF/tests/data/");

    // parfile
    parFile = basePath + static_cast<string>("/parameters.ini");

    // datafile
    dataFile = basePath + static_cast<string>("/galaxy_coordinates.dat");

    // randfile
    randFile = basePath + static_cast<string>("/random_coordinates.dat");

    // redefine adding path
    refCorr1Dname = basePath + refCorr1Dname;
    refCorr2Dname = basePath + refCorr2Dname;
    refPair1Dname = basePath + refPair1Dname;
    refPair2Dname = basePath + refPair2Dname;
    refCrossPair1Dname = basePath + refCrossPair1Dname;
    refCrossPair2Dname = basePath + refCrossPair2Dname;

    // path for test local data
    tmpFitsName = fs::unique_path("test_fits.fits").native();

    // path for test local data
    tmpFits2Name = fs::unique_path("test_fits2.fits").native();

    // path for test local data TXT
    tmpTxtName = fs::unique_path("test_txt.txt").native();

    // path for test local data TXT
    tmpTxt2Name = fs::unique_path("test_txt2.txt").native();

    // path for test local data
    tmpCorrName = fs::unique_path("test_corr.fits").native();

    // path for test local data
    tmpPairName = fs::unique_path("test_pair.fits").native();

    // path for test local data
    tmpCorr2Name = fs::unique_path("test_corr2.fits").native();
  }

  void build2PFC(const string& confname) {
    Parameters localParams = setupParameters(confname);
    tpcf = LinkedList(localParams);
  }

  // le3gc::LE3_GC_2PCF::ProductsIO buildProducts(const string& dir, const string& pipeline) {
  //   le3gc::LE3_GC_2PCF::ProductsIO pfio(dir,pipeline);
  //   return pfio;
  // }

  // setup input configuration file
  Parameters setupParameters (const string& confname) {

    // open file
    fstream tmpfile(tmpTxtName, std::ios::out);

    tmpfile << "[Path]" << endl;
    tmpfile << "output = release" << endl;

    tmpfile << "[2PCF]" << endl;

    // specific configuration
    string name = "NULL";
    if ("NONE" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeNONE << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = UNUSED" << endl;
      tmpfile << "bin2_num   = UNUSED" << endl;
      tmpfile << "bin2_min   = UNUSED" << endl;
      tmpfile << "bin2_max   = UNUSED" << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("AUTO_1D" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << type1D << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type = UNUSED" << endl;
      tmpfile << "bin2_num  = UNUSED" << endl;
      tmpfile << "bin2_min  = UNUSED" << endl;
      tmpfile << "bin2_max  = UNUSED" << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("AUTO_2DCART" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << type2Dc << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = " << max2 << endl;

    } else if ("AUTO_2DPOL" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << type2Dp << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("CROSS_1D" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeC1D << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type = UNUSED" << endl;
      tmpfile << "bin2_num  = UNUSED" << endl;
      tmpfile << "bin2_min  = UNUSED" << endl;
      tmpfile << "bin2_max  = UNUSED" << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    } else if ("CROSS_2DCART" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeC2Dc << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = " << max2 << endl;

    } else if ("CROSS_2DPOL" == confname) {
      tmpfile << "method = KD_TREE" << endl;
      tmpfile << "statistics = " << typeC2Dp << endl;
      tmpfile << "bin1_type  = " << typeLIN << endl;
      tmpfile << "bin1_num   = " << n1   << endl;
      tmpfile << "bin1_min   = " << min1 << endl;
      tmpfile << "bin1_max   = " << max1 << endl;
      tmpfile << "bin2_type  = " << typeLOG << endl;
      tmpfile << "bin2_num   = " << n2   << endl;
      tmpfile << "bin2_min   = " << min2 << endl;
      tmpfile << "bin2_max   = " << max2 << endl;
      tmpfile << "PI_MAX     = UNUSED" << endl;

    }
    
    tmpfile << "use_weight = true" << endl;
    tmpfile << "ASCII_out = false" << endl;
    tmpfile << "split_factor = 1" << endl;

    tmpfile << "[Cosmology]" << endl;
    tmpfile << "cosmology_ID = LCDM" << endl;

    tmpfile.close();

    return Parameters(tmpTxtName);
  }


  // load a basic catalog with 3 elements as test DATA catalog
  void loadTestDataCat() {

    // build parameter file for catalog configuration
    fstream tmpfile(tmpTxtName, std::ios::out);
    tmpfile << "[Catalog]" << endl;
    tmpfile << "constant_depth = true" << endl;
    tmpfile << "nbar_tabulated = true" << endl;
    tmpfile << "[Catalog.Galaxy]" << endl;
    tmpfile << "name = " << tmpTxtName << endl;
    tmpfile << "filename = " << tmpTxtName << endl;
    tmpfile << "coordinates = CARTESIAN" << endl;
    tmpfile << "angle_units = DEG" << endl;
    tmpfile << "coord1 = 1" << endl;
    tmpfile << "coord2 = 2" << endl;
    tmpfile << "coord3 = 3" << endl;
    tmpfile << "density = 4" << endl;
    tmpfile << "weight = 5" << endl;
    tmpfile << "mask = 6" << endl;
    tmpfile.clear();
    tmpfile.close();

    // configure catalog
    Parameters par(tmpTxtName);
    datacat = CatalogAscii(le3gc::CatalogType::DATA);
    datacat.configure(par, cosmo);

    // build catalog ASCII file
    tmpfile.open(tmpTxtName, std::ios::out);
    tmpfile << "-3.0   -2.0   -1.0    2.0   1.0" << endl; // min pos
    tmpfile << " 1.0    2.0    3.0    5.0   1.0" << endl; // max pos
    tmpfile << "   0      0      0      0     0" << endl; // empty
    for (int i=0; i<100; ++i)
      tmpfile << "1.0    1.0    1.0    1.0    1.0" << endl; // other 100 lines to test SetupPairCount() 
    tmpfile.close();

    // load catalog
    datacat.openFile(tmpTxtName);
    datacat.loadFromFile();
  }

  // load a basic catalog with 4 elements as test RANDOM catalog
  void loadTestRandCat() {

    fstream tmpfile(tmpTxtName, std::ios::out);
    tmpfile << "[Catalog]" << endl;
    tmpfile << "constant_depth = true" << endl;
    tmpfile << "nbar_tabulated = true" << endl;
    tmpfile << "[Catalog.Random]" << endl;
    tmpfile << "name = " << tmpTxtName << endl;
    tmpfile << "filename = " << tmpTxtName << endl;
    tmpfile << "coordinates = CARTESIAN" << endl;
    tmpfile << "angle_units = DEG" << endl;
    tmpfile << "coord1 = 1" << endl;
    tmpfile << "coord2 = 2" << endl;
    tmpfile << "coord3 = 3" << endl;
    tmpfile << "density = 4" << endl;
    tmpfile << "weight = 5" << endl;
    tmpfile << "mask = 6" << endl;
    tmpfile.clear();
    tmpfile.close();

    // configure catalog
    Parameters par(tmpTxtName);
    randcat = CatalogAscii(le3gc::CatalogType::RAND);
    randcat.configure(par, cosmo);

    // rewrite file with catalog data
    tmpfile.open(tmpTxtName, std::ios::out);
    tmpfile << "-3.0   -2.0   -1.0    2.0   1.0" << endl; // min pos
    tmpfile << " 1.0    2.0    3.0    5.0   1.0" << endl; // max pos
    tmpfile << "   0      0      0      0     0" << endl; // empty
    tmpfile << "-5.0   -1.0   -3.0    2.0   1.0" << endl; // new min2 on x,z
    for (int i=0; i<100; ++i)
      tmpfile << "1.0    1.0    1.0    1.0    1.0" << endl; // other 100 lines to test SetupPairCount() 
    tmpfile.close();

    // load catalog
    randcat.openFile(tmpTxtName);
    randcat.loadFromFile();
  }

  void clean() {
    if (true == fs::exists(tmpFitsName)) {
        fs::remove(tmpFitsName);
    }
    if (true == fs::exists(tmpFits2Name)) {
        fs::remove(tmpFits2Name);
    }
    if (true == fs::exists(tmpTxtName)) {
        fs::remove(tmpTxtName);
    }
    if (true == fs::exists(tmpTxt2Name)) {
        fs::remove(tmpTxt2Name);
    }
    if (true == fs::exists(tmpCorrName)) {
        fs::remove(tmpCorrName);
    }
    if (true == fs::exists(tmpPairName)) {
        fs::remove(tmpPairName);
    }
    if (true == fs::exists(tmpCorr2Name)) {
        fs::remove(tmpCorr2Name);
    }
  }

  ~LinkedListEnv() {
    clean();
  };

} e;

//-----------------------------------------------------------------------------

BOOST_AUTO_TEST_SUITE (LTwoPointCorrelation_test)

//-----------------------------------------------------------------------------

// -------------------------------------------------------------------------- //

// test the SetupPairCount function
BOOST_AUTO_TEST_CASE(setup_pair_count_test) {

    logger.info();
    logger.info() << "--> TwoPointCorrelation: set up 2PCF for specific correlation estimator";
    
    Parameters params = e.setupParameters("AUTO_1D");
 
    std::unique_ptr<TwoPointCorrelation> twoPoint = TwoPointCorrelation::make2PCF(params);
    
    // test the SetupPairCount function with 1 object
    twoPoint->SetupPairCount(1);
    
    BOOST_CHECK_EQUAL(twoPoint->m_box.nobjects, 1);
}
  
//-----------------------------------------------------------------------------

// -------------------------------------------------------------------------- //

 // test the SetupPairCount function
BOOST_AUTO_TEST_CASE(get_pair_count_test) {

    logger.info();
    logger.info() << "--> TwoPointCorrelation: set up 2PCF for specific correlation estimator";
    
    Parameters params = e.setupParameters("AUTO_1D");
 
    std::unique_ptr<TwoPointCorrelation> twoPoint = TwoPointCorrelation::make2PCF(params);
    
    // test the SetupPairCount function with 1 object
    twoPoint->SetupPairCount(1);
    
    BOOST_CHECK_EQUAL(twoPoint->m_box.nobjects, 1);
}
  
//-----------------------------------------------------------------------------

// -------------------------------------------------------------------------- //
// test correlation FITS 1D: uses global object configuration
BOOST_AUTO_TEST_CASE(read_pair_fits_1D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: read pair fits 1D";

    // load configuration AUTO_1D (not needed to reload, just for safety)
    e.build2PFC("AUTO_1D");

    // dummy correlation vector
    vector<double> vec(e.tpcf.m_nBin1D, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0 , 1.0, 
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    double maxp;
    vector<double> vecDDread = e.tpcf.readPairs(e.tmpFitsName,"DD",maxp);
    vector<double> vecDRread = e.tpcf.readPairs(e.tmpFitsName,"DR",maxp);
    vector<double> vecRRread = e.tpcf.readPairs(e.tmpFitsName,"RR",maxp);

    BOOST_CHECK_EQUAL_COLLECTIONS(vecDDread.begin(), vecDDread.end(), vec.begin(), vec.end());
    BOOST_CHECK_EQUAL_COLLECTIONS(vecDRread.begin(), vecDRread.end(), vec.begin(), vec.end());
    BOOST_CHECK_EQUAL_COLLECTIONS(vecRRread.begin(), vecRRread.end(), vec.begin(), vec.end());
    BOOST_REQUIRE_CLOSE(1.0, maxp, 1.e-7);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 1D: uses global object configuration
BOOST_AUTO_TEST_CASE(write_pair_fits_1D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: write pair fits 1D";

    // load configuration AUTO_1D (not needed to reload, just for safety)
    e.build2PFC("AUTO_1D");

    // dummy correlation vector
    vector<double> vec(e.tpcf.m_nBin1D, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0 , 1.0, 
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);

    // check PAIRS table exists, has only 1D scale and correct datasum
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE"));
    // check NO 2D column
    BOOST_CHECK(false == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("DD").size());
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("DR").size());
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("RR").size());

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(write_pair_fits_2D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair fits 2D";

    // load configuration AUTO_2DCart
    e.build2PFC("AUTO_2DCART");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFits2Name, vec, vec, vec, vec, 1.0, 1.0, 1.0, 1.0,
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFits2Name, le3gc::FitsMode::READ);

    // table exist
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_1D"));
    // check NO 2D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("DD").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("DR").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("RR").size());

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(write_pair_fits_2DP_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair fits 2D polar";

    // load configuration AUTO_2DPol
    e.build2PFC("AUTO_2DPOL");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0, 1.0,
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);

    // table exist
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_1D"));
    // check NO 2D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("DD").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("DR").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("RR").size());

    fit.closeFits();
    e.clean();
}
// -------------------------------------------------------------------------- //

// test correlation FITS none
BOOST_AUTO_TEST_CASE(pair_fits_none_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair fits NONE";

    // default kd-tree
    e.build2PFC("NONE");

    // random number
    size_t nbin = 3;

    // correlation vector: ANY length
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0, 1.0,
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);
    // table exist
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(false == fit.hasColumn("SCALE_1D"));
    // check NO 2D column
    BOOST_CHECK(false == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK(false == fit.hasColumn("PAIRS"));

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 1D: uses global object configuration
BOOST_AUTO_TEST_CASE(write_pair_fits_cross_1D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: write pair fits 1D";

    // load configuration AUTO_1D (not needed to reload, just for safety)
    e.build2PFC("CROSS_1D");

    // dummy correlation vector
    vector<double> vec(e.tpcf.m_nBin1D, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0 , 1.0, 
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);

    // check PAIRS table exists, has only 1D scale and correct datasum
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE"));
    // check NO 2D column
    BOOST_CHECK(false == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("D1D2").size());
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("D1R2").size());
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("R1D2").size());
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, fit.getColumn<double>("R1R2").size());

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(write_pair_fits_cross_2D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair fits 2D";

    // load configuration AUTO_2DCart
    e.build2PFC("CROSS_2DCART");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0, 1.0,
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);

    // table exist
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_1D"));
    // check NO 2D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("D1D2").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("D1R2").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("R1D2").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("R1R2").size());

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(write_pair_fits_cross_2DP_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair fits 2D polar";

    // load configuration AUTO_2DPol
    e.build2PFC("CROSS_2DPOL");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairs(e.tmpFitsName, vec, vec, vec, vec, 1.0, 1.0, 1.0, 1.0,
              e.datacat, e.datacat, e.datacat, e.datacat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);

    // table exist
    fit.gotoTable("PAIRS");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_1D"));
    // check NO 2D column
    BOOST_CHECK(true == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("D1D2").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("D1R2").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("R1D2").size());
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("R1R2").size());

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test pairs TXT 1D
BOOST_AUTO_TEST_CASE(write_pair_ascii_1D_test) 
{
    logger.info();
    logger.info() << "--> LINKEDLIST: pairs ascii 1D";

    // load configuration AUTO_1D
    e.build2PFC("AUTO_1D");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairsASCII(e.tmpTxtName, vec, vec, vec, vec, vec);

    // open reference
    std::ifstream ref(e.refPair1Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);

      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }
   
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(write_pair_ascii_2D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair ascii 2D";

    // load configuration AUTO_2DCart
    e.build2PFC("AUTO_2DCART");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairsASCII(e.tmpTxtName, vec, vec, vec, vec, vec);

    // open reference
    std::ifstream ref(e.refPair2Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);

      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D Polar
/* Disabled: wrong mu scale
BOOST_AUTO_TEST_CASE(write_pair_ascii_2DP_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair ascii 2D polar";

    // load configuration AUTO_2DCart
    e.build2PFC("AUTO_2DPOL");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairsASCII(e.tmpTxtName, vec, vec, vec, vec, vec);

    // open reference
    std::ifstream ref(e.refPair2Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);

      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }

    e.clean();
}
*/

// -------------------------------------------------------------------------- //

// test pairs TXT 1D
BOOST_AUTO_TEST_CASE(write_pair_ascii_cross_1D_test) 
{
    logger.info();
    logger.info() << "--> LINKEDLIST: pairs ascii coss 1D";

    // load configuration AUTO_1D
    e.build2PFC("CROSS_1D");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairsASCII(e.tmpTxtName, vec, vec, vec, vec, vec);

    // open reference
    std::ifstream ref(e.refCrossPair1Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);

      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }
   
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(write_pair_ascii_cross_2D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair ascii cross 2D";

    // load configuration AUTO_2DCart
    e.build2PFC("CROSS_2DCART");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairsASCII(e.tmpTxtName, vec, vec, vec, vec, vec);

    // open reference
    std::ifstream ref(e.refCrossPair2Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);

      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D Polar
/* Disabled: wrong mu scale
BOOST_AUTO_TEST_CASE(write_pair_ascii_cross_2DP_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: pair ascii cross 2D polar";

    // load configuration AUTO_2DCart
    e.build2PFC("CROSS_2DPOL");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writePairsASCII(e.tmpTxtName, vec, vec, vec, vec, vec);

    // open reference
    std::ifstream ref(e.refCrossPair2Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);

      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }

    e.clean();
}
*/

// -------------------------------------------------------------------------- //

// check correlation computation for AUTO_1D - DEFAULT
BOOST_AUTO_TEST_CASE(compute_correlation_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: compute correlation 1D";

    // load AUTO_1D
    e.build2PFC("AUTO_1D");

    // set 1D
    e.tpcf.m_nBin1D = 2;
    e.tpcf.m_min1D = 1.0;
    e.tpcf.m_max1D = 2.0;

    // set 2D
    e.tpcf.m_nBin2D = 1;
    e.tpcf.m_min2D = 1.0;
    e.tpcf.m_max2D = 1.0;

    e.tpcf.m_statistics = CorrelationType::AUTO_1D;

    // set pair counts
    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;
    vector<double> dd(obj, 1.0);
    vector<double> dr(obj, 1.0);
    vector<double> rr(obj, 1.0);
    vector<double> xi = e.tpcf.computeCorrelation(dd, dr, rr, dd, 1.0, 4.0, 1.0, 1.0);

    // check correlation vector length Dim1
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, xi.size());
    // check correlation values
    for (double c : xi ) {
      BOOST_REQUIRE_CLOSE(1.5, c, 0.00001);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// check correlation computation for AUTO_1D - DEFAULT
BOOST_AUTO_TEST_CASE(compute_correlation_2D_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: compute correlation 2D";

    // load AUTO_1D
    e.build2PFC("AUTO_2DCART");

    // set 1D
    e.tpcf.m_nBin1D = 3;
    e.tpcf.m_min1D = 1.0;
    e.tpcf.m_max1D = 2.0;

    // set 2D
    e.tpcf.m_nBin2D = 2;
    e.tpcf.m_min2D = 1.0;
    e.tpcf.m_max2D = 2.0;

    e.tpcf.m_statistics = CorrelationType::AUTO_2DCART;

    // set pair counts
    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;
    vector<double> dd(obj, 1.0);
    vector<double> dr(obj, 1.0);
    vector<double> rr(obj, 1.0);
    vector<double> xi = e.tpcf.computeCorrelation(dd, dr, rr, dd, 1.0, 4.0, 1.0, 1.0);

    // check correlation vector length Dim1
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D + e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, xi.size());

    // check correlation values
    int i = 0;
    for (double c : xi ) {
      i++;
      if (i <= e.tpcf.m_nBin1D*e.tpcf.m_nBin2D) BOOST_REQUIRE_CLOSE(1.5, c, 0.00001);
      else BOOST_REQUIRE_CLOSE(3.0, c, 0.00001);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// check correlation computation for AUTO_1D - DEFAULT

BOOST_AUTO_TEST_CASE(compute_correlation_2DC_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: compute correlation 2D polar";

    // load AUTO_1D
    e.build2PFC("AUTO_2DPOL");

    // set 1D
    e.tpcf.m_nBin1D = 3;
    e.tpcf.m_min1D = 1.0;
    e.tpcf.m_max1D = 2.0;

    // set 2D
    e.tpcf.m_nBin2D = 2;
    e.tpcf.m_min2D = -1.0;
    e.tpcf.m_max2D = 1.0;
    e.tpcf.m_scale2D = BinningType::LIN;

    e.tpcf.m_statistics = CorrelationType::AUTO_2DPOL;

    // set pair counts
    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;
    vector<double> dd(obj, 1.0);
    vector<double> dr(obj, 1.0);
    vector<double> rr(obj, 1.0);
    vector<double> xi = e.tpcf.computeCorrelation(dd, dr, rr, dd, 1.0, 4.0, 1.0, 1.0);

    // check correlation vector length Dim1
    BOOST_CHECK_EQUAL(5*e.tpcf.m_nBin1D + e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, xi.size());

    // check correlation values
    int i = 0;
    for (double c : xi ) {
      i++;
      logger.info() << c << " " << i;
      if (i <= e.tpcf.m_nBin1D*e.tpcf.m_nBin2D) BOOST_REQUIRE_CLOSE(1.5, c, 0.00001);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// check correlation computation for AUTO_1D - DEFAULT
BOOST_AUTO_TEST_CASE(compute_correlation_cross_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: compute cross correlation 1D";

    // load AUTO_1D
    e.build2PFC("CROSS_1D");

    // set 1D
    e.tpcf.m_nBin2D = 2;
    e.tpcf.m_min2D = 1.0;
    e.tpcf.m_max2D = 2.0;

    // set 2D
    e.tpcf.m_nBin2D = 1;
    e.tpcf.m_min2D = 1.0;
    e.tpcf.m_max2D = 1.0;

    e.tpcf.m_statistics = CorrelationType::CROSS_1D;

    // set pair counts
    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;
    vector<double> d1d2(obj, 1.0);
    vector<double> d1r2(obj, 1.0);
    vector<double> r1d2(obj, 1.0);
    vector<double> r1r2(obj, 1.0);
    vector<double> xi = e.tpcf.computeCorrelation(d1d2, d1r2, r1d2, r1r2, 4.0, 4.0, 4.0, 4.0);

    // check correlation vector length Dim1
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, xi.size());
    // check correlation values
    for (double c : xi ) {
      BOOST_REQUIRE_CLOSE(0, c, 0.00001);
    }

    e.clean();
}
// -------------------------------------------------------------------------- //

// check correlation computation for AUTO_1D - DEFAULT
BOOST_AUTO_TEST_CASE(compute_correlation_cross_2D_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: compute cross correlation 2D";

    // load AUTO_1D
    e.build2PFC("CROSS_2DCART");

    // set 1D
    e.tpcf.m_nBin1D = 3;
    e.tpcf.m_min1D = 1.0;
    e.tpcf.m_max1D = 2.0;

    // set 2D
    e.tpcf.m_nBin2D = 2;
    e.tpcf.m_min2D = 1.0;
    e.tpcf.m_max2D = 2.0;

    e.tpcf.m_statistics = CorrelationType::CROSS_2DCART;

    // set pair counts
    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;
    vector<double> d1d2(obj, 1.0);
    vector<double> d1r2(obj, 1.0);
    vector<double> r1d2(obj, 1.0);
    vector<double> r1r2(obj, 1.0);
    vector<double> xi = e.tpcf.computeCorrelation(d1d2, d1r2, r1d2, r1r2, 4.0, 4.0, 4.0, 4.0);

    // check correlation vector length Dim1
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D + e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, xi.size());

    // check correlation values
    /*
    int i = 0;
    for (double c : xi ) {
      i++;
      if (i <= e.tpcf.m_nBin1D*e.tpcf.m_nBin2D) BOOST_REQUIRE_CLOSE(1.5, c, 0.00001);
      else BOOST_REQUIRE_CLOSE(3.0, c, 0.00001);
    }
    */

    e.clean();
}

// -------------------------------------------------------------------------- //

// check correlation computation for AUTO_1D - DEFAULT
BOOST_AUTO_TEST_CASE(compute_correlation_cross_2DC_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: compute cross correlation 2D polar";

    // load AUTO_1D
    e.build2PFC("CROSS_2DPOL");

    // set 1D
    e.tpcf.m_nBin1D = 3;
    e.tpcf.m_min1D = 1.0;
    e.tpcf.m_max1D = 2.0;

    // set 2D
    e.tpcf.m_nBin2D = 2;
    e.tpcf.m_min2D = 0.0;
    e.tpcf.m_max2D = 1.0;
    e.tpcf.m_scale2D = BinningType::LIN;

    e.tpcf.m_statistics = CorrelationType::CROSS_2DPOL;

    // set pair counts
    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;
    vector<double> d1d2(obj, 1.0);
    vector<double> d1r2(obj, 1.0);
    vector<double> r1d2(obj, 1.0);
    vector<double> r1r2(obj, 1.0);
    vector<double> xi = e.tpcf.computeCorrelation(d1d2, d1r2, r1d2, r1r2, 4.0, 4.0, 4.0, 4.0);

    // check correlation vector length Dim1
    BOOST_CHECK_EQUAL(5*e.tpcf.m_nBin1D + e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, xi.size());

    // check correlation values
    /*
    int i = 0;
    for (double c : xi ) {
      i++;
      //logger.info() << c << " " << i;
      //if (i <= e.tpcf.m_nBin1D*e.tpcf.m_nBin2D) BOOST_REQUIRE_CLOSE(1.5, c, 0.00001);
      //else BOOST_REQUIRE_CLOSE(0.0, c, 0.00001);
      //if (i == 5*e.tpcf.m_nBin1D + e.tpcf.m_nBin1D*e.tpcf.m_nBin2D) BOOST_REQUIRE_CLOSE(1.5, c, 0.00001);
    }
    */

    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 1D
BOOST_AUTO_TEST_CASE(correlation_fits_1D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: write correlation fits 1D";

    // reset to AUTO_1D
    e.build2PFC("AUTO_1D");

    // get number of bin used
    size_t nbin = e.tpcf.m_nBin1D;

    // dummy correlation vector
    vector<double> vec(nbin, 127.0);

    // write correlation
    e.tpcf.writeCorrelation(e.tmpFitsName, e.tmpFitsName, vec, 
                e.datacat, e.randcat, e.datacat, e.randcat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);
    // table exist
    fit.gotoTable("CORRELATION");
    // has 1D column
    BOOST_CHECK(true == fit.hasColumn("SCALE"));
    // check NO 2D column
    BOOST_CHECK(false == fit.hasColumn("SCALE_2D"));
    // check XI column length
    BOOST_CHECK_EQUAL(nbin, fit.getColumn<double>("XI").size());

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation TXT 1D
BOOST_AUTO_TEST_CASE(correlation_ascii_1D_test) 
{
    logger.info();
    logger.info() << "--> LINKEDLIST: write correlation ascii 1D";

    // setup AUTO_1D
    e.build2PFC("AUTO_1D");

    // correlation vector
    vector<double> vec(e.tpcf.m_nBin1D, 127.0);

    // write correlation
    e.tpcf.writeCorrelationASCII(vec, e.tmpTxtName, e.tmpTxtName);

    std::ifstream ref(e.refCorr1Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);
      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }

      BOOST_CHECK(lineref == linetest);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS 2D
BOOST_AUTO_TEST_CASE(correlation_fits_2D_test) 
{
  // set 2D
  logger.info();
  logger.info() << "--> LINKEDLIST: write correlation fits 2D";

  // reset to AUTO_2DCart
  e.build2PFC("AUTO_2DCART");

  // dummy correlation vector
  vector<double> vec(1+e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, 127.0);

  // write correlation
  e.tpcf.writeCorrelation(e.tmpCorrName, e.tmpCorr2Name, vec, 
              e.datacat, e.randcat, e.datacat, e.randcat, cosmo);

  // test only checksum for data:
  Fits fit(e.tmpCorrName, le3gc::FitsMode::READ);
  // table exist
  fit.gotoTable("CORRELATION");
  // has 1D column
  BOOST_CHECK(true == fit.hasColumn("SCALE_1D"));
  // check NO 2D column
  BOOST_CHECK(true == fit.hasColumn("SCALE_2D"));
  // check XI column length
  BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, fit.getColumn<double>("XI").size());

  fit.closeFits();
  e.clean();
}

// --------------------------------------------------------------------------- //

// test correlation TXT 2D
BOOST_AUTO_TEST_CASE(correlation_ascii_2D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: write correlation ascii 2D";

    e.build2PFC("AUTO_2DCART");

    size_t obj = e.tpcf.m_nBin1D*e.tpcf.m_nBin2D;

    // correlation vector
    vector<double> vec(obj, 127.0);

    // write correlation
    e.tpcf.writeCorrelationASCII(vec, e.tmpTxtName, e.tmpTxt2Name);

    std::ifstream ref(e.refCorr2Dname.c_str());
    std::ifstream test(e.tmpTxtName.c_str());

    logger.info() << e.refCorr2Dname.c_str();

    string lineref;
    string linetest;
    while (false == test.eof()) {
      std::getline(ref, lineref);
      std::getline(test, linetest);
      if (lineref != linetest) {
        logger.info() << lineref << " =! " << linetest;
      }
      BOOST_CHECK(lineref == linetest);
    }

    e.clean();
}

// -------------------------------------------------------------------------- //

// test correlation FITS none
BOOST_AUTO_TEST_CASE(correlation_fits_none_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: correlation fits none";

    // --> default is AUTO_1D
    e.build2PFC("NONE");

    // correlation vector
    vector<double> vec(e.tpcf.m_nBin1D, 127.0);

    // write correlation
    e.tpcf.writeCorrelation(e.tmpFitsName, e.tmpFitsName, vec, 
                 e.datacat, e.randcat, e.datacat, e.randcat, cosmo);

    // test only checksum for data:
    Fits fit(e.tmpFitsName, le3gc::FitsMode::READ);

    // table exist
    fit.gotoTable("CORRELATION");

    // NO columns added
    // has 1D column
    //BOOST_CHECK(true == fit.hasColumn("SCALE"));
    // check NO 2D column
    //BOOST_CHECK(false == fit.hasColumn("SCALE_2D"));
    // check XI column length
    //BOOST_CHECK(true == fit.hasColumn("XI"));

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS file
BOOST_AUTO_TEST_CASE(cat_info_to_fits_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: cat info to FITS";

  // still AUTO_1D
  e.build2PFC("AUTO_1D");

  // default parameters AUTO_1D
  e.loadTestDataCat();

  // create empty fitsfile
  Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);
  string table = "test";

  // write FITS info
  e.tpcf.writeCatInfoToFITS(e.tmpFitsName, table, "D", e.datacat);

  // open file for check
  fit.openFits(e.tmpFitsName);
  fit.gotoTable(table);

  // test keywords
  BOOST_CHECK(fit.getKey<string>("D_NAME") == e.tmpTxtName);
  //BOOST_CHECK(fit.getKey<string>("D_ID") == e.tmpTxtName);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("D_NOBJ"), 103, 0.00001);

  fit.closeFits();
  e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS file
BOOST_AUTO_TEST_CASE(info_to_fits_1D_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: info to FITS 1D";

  // still AUTO_1D
  e.build2PFC("AUTO_1D");

  // create empty fitsfile
  Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);
  string table = "test";

  // write FITS info
  string str_stat = "AUTO_1D";
  e.tpcf.writeInfoToFITS(e.tmpFitsName, table, str_stat);

  // open file for check
  fit.openFits(e.tmpFitsName);
  fit.gotoTable(table);

  // test keywords
  BOOST_CHECK(fit.getKey<string>("BIN_TYPE") == "LIN");
  BOOST_CHECK_EQUAL(fit.getKey<int>("BIN_NUM"), e.n1);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN_MIN"), e.min1, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN_MAX"), e.max1, 0.00001);

  fit.closeFits();
  e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS 2D file
BOOST_AUTO_TEST_CASE(info_to_fits_2D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: info to FITS 2D";

    // still AUTO_2DCart
    e.build2PFC("AUTO_2DCART");

    // crete empty fitsfile
    Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);
    string table = "test";

    // write FITS info
    string str_stat = "TWO_DIM_CART";
    e.tpcf.writeInfoToFITS(e.tmpFitsName, table, str_stat);

    // open file for check
    fit.openFits(e.tmpFitsName);
    fit.gotoTable("test");

    // test keywords
    BOOST_CHECK(fit.getKey<string>("BIN1TYPE") == "LIN");
    BOOST_CHECK_EQUAL(fit.getKey<int>("BIN1NUM"), e.n1);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN1MIN"), e.min1, 0.00001);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN1MAX"), e.max1, 0.00001);
    BOOST_CHECK(fit.getKey<string>("BIN2TYPE") == "LOG");
    BOOST_CHECK_EQUAL(fit.getKey<int>("BIN2NUM"), e.n2);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN2MIN"), e.min2, 0.00001);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN2MAX"), e.max2, 0.00001);

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS 2D polar file 
BOOST_AUTO_TEST_CASE(info_to_fits_2DP_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: info to FITS 2D polar";

    // still AUTO_2DCart
    e.build2PFC("AUTO_2DPOL");

    // crete empty fitsfile
    Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);

    // write FITS info
    string str_stat = "TWO_DIM_POL";
    e.tpcf.writeInfoToFITS(e.tmpFitsName, "test", str_stat);

    // open file for check
    fit.openFits(e.tmpFitsName);
    fit.gotoTable("test");

    // test keywords
    BOOST_CHECK(fit.getKey<string>("BIN1TYPE") == "LIN");
    BOOST_CHECK_EQUAL(fit.getKey<int>("BIN1NUM"), e.n1);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN1MIN"), e.min1, 0.00001);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN1MAX"), e.max1, 0.00001);
    BOOST_CHECK(fit.getKey<string>("BIN2TYPE") == "LIN");
    BOOST_CHECK_EQUAL(fit.getKey<int>("BIN2NUM"), e.n2);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN2MIN"), 1, 0.00001);
    BOOST_REQUIRE_CLOSE(fit.getKey<double>("BIN2MAX"), 1, 0.00001);

    fit.closeFits();
    e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS file
BOOST_AUTO_TEST_CASE(pairs_info_to_fits_1D_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: pairs info to FITS 1D";

  // still AUTO_1D
  e.build2PFC("AUTO_1D");

  // create empty fitsfile
  Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);
  string table = "test";

  // write FITS info
  e.tpcf.writePairsInfoToFITS(e.tmpFitsName, table, 1.0, 2.0, 3.0, 4.0, 5.0);

  // open file for check
  fit.openFits(e.tmpFitsName);
  fit.gotoTable(table);

  // test keywords
  
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_DD"), 1.0, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_DR"), 2.0, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_RR"), 3.0, 0.00001);

  fit.closeFits();
  e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS file
BOOST_AUTO_TEST_CASE(pairs_info_to_fits_2D_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: pairs info to FITS 2D";

  // still AUTO_1D
  e.build2PFC("AUTO_2DCART");

  // create empty fitsfile
  Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);
  string table = "test";

  // write FITS info
  e.tpcf.writePairsInfoToFITS(e.tmpFitsName, table, 1.0, 2.0, 3.0, 4.0, 5.0);

  // open file for check
  fit.openFits(e.tmpFitsName);
  fit.gotoTable(table);

  // test keywords
  
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_DD"), 1.0, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_DR"), 2.0, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_RR"), 3.0, 0.00001);

  fit.closeFits();
  e.clean();
}

// -------------------------------------------------------------------------- //

// test info to FITS file
BOOST_AUTO_TEST_CASE(pairs_info_to_fits_2DP_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: pairs info to FITS 2D polar";

  e.build2PFC("AUTO_2DPOL");

  // create empty fitsfile
  Fits fit(e.tmpFitsName, le3gc::FitsMode::WRITE);
  string table = "test";

  // write FITS info
  e.tpcf.writePairsInfoToFITS(e.tmpFitsName, table, 1.0, 2.0, 3.0, 4.0, 5.0);

  // open file for check
  fit.openFits(e.tmpFitsName);
  fit.gotoTable(table);

  // test keywords
  
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_DD"), 1.0, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_DR"), 2.0, 0.00001);
  BOOST_REQUIRE_CLOSE(fit.getKey<double>("PAIR_RR"), 3.0, 0.00001);

  fit.closeFits();
  e.clean();
}

// -------------------------------------------------------------------------- //

// test get axis 1D
BOOST_AUTO_TEST_CASE(get_axis_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get axis";

    // Still AUTO_2DCart
    e.build2PFC("AUTO_1D");

    // get scale along 1D axis
    vector<double> vec = e.tpcf.getAxisScale(le3gc::LE3_GC_2PCF::AxisType::DIM1);

    // start from 0
    BOOST_REQUIRE_CLOSE(vec.front(), (0.05 + e.tpcf.m_min1D), 00001);
    // end max1D
    BOOST_REQUIRE_CLOSE(vec.back(), e.tpcf.m_max1D, 00001);
    // nBin
    BOOST_CHECK_EQUAL(vec.size(), e.tpcf.m_nBin1D);

    e.clean();

    // Still AUTO_2DCart
    e.build2PFC("AUTO_2DCART");

    logger.info();
    logger.info() << "--> LINKEDLIST: get axis 2D LOG";

    // get scale along 1D axis
    vector<double> vec2 = e.tpcf.getAxisScale(le3gc::LE3_GC_2PCF::AxisType::DIM2);
    // start from 0
    BOOST_REQUIRE_CLOSE(vec2.front(), 0.25118864315095801, 00001);
    // end max1D
    BOOST_REQUIRE_CLOSE(vec2.back(), 398.10717055349733, 00001);
    // nBin
    BOOST_CHECK_EQUAL(vec2.size(), e.tpcf.m_nBin2D);

    e.clean();

    // Still AUTO_2DCart
    e.build2PFC("AUTO_2DPOL");

    logger.info();
    logger.info() << "--> LINKEDLIST: get axis 2DP LOG";

    // get scale along 1D axis
    vec2 = e.tpcf.getAxisScale(le3gc::LE3_GC_2PCF::AxisType::DIM2);
    // start from 0
    BOOST_REQUIRE_CLOSE(vec2.front(), -0.8, 0.0001);
    // end max1D
    BOOST_REQUIRE_CLOSE(vec2.back(), 0.8, 0.0001);
    // nBin
    BOOST_CHECK_EQUAL(vec2.size(), e.tpcf.m_nBin2D);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test initBox
BOOST_AUTO_TEST_CASE(init_box_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: init box";

    e.build2PFC("AUTO_1D");

    le3gc::dVector boxmin;
    le3gc::dVector boxmax;

    boxmin[0] = boxmin[1] = boxmin[2] = 0;
    boxmax[0] = boxmax[1] = boxmax[2] = 1;

    // get scale along 1D axis
    e.tpcf.initBox(boxmin,boxmax);

    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.sizeCell[0], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.sizeCell[1], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.sizeCell[2], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.sizeCell[3], 0.0, 0.000001);

    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.rSizeCell[0], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.rSizeCell[1], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.rSizeCell[2], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.rSizeCell[3], 0.0, 0.000001);

    BOOST_CHECK_EQUAL(e.tpcf.m_box.nCell[0], 0);
    BOOST_CHECK_EQUAL(e.tpcf.m_box.nCell[1], 0);
    BOOST_CHECK_EQUAL(e.tpcf.m_box.nCell[2], 0);
    BOOST_CHECK_EQUAL(e.tpcf.m_box.nCell[3], 0);

    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.defaultBoxCell, 0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.maxScale, 0, 0.000001);
    BOOST_CHECK_EQUAL(0, e.tpcf.m_box.nobjects);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test from_box_vector
BOOST_AUTO_TEST_CASE(from_box_vector_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: box from vector";

    e.build2PFC("AUTO_1D");

    BoxType box;
    le3gc::dVector boxmin;
    le3gc::dVector boxmax;

    boxmin[0] = boxmin[1] = boxmin[2] = 0;
    boxmax[0] = boxmax[1] = boxmax[2] = 1;

    // get scale along 1D axis
    box = e.tpcf.boxFromVector(boxmin,boxmax,10);

    BOOST_REQUIRE_CLOSE(box.sizeCell[0], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.sizeCell[1], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.sizeCell[2], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.sizeCell[3], 0.0, 0.000001);

    BOOST_REQUIRE_CLOSE(box.rSizeCell[0], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.rSizeCell[1], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.rSizeCell[2], 0.0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.rSizeCell[3], 0.0, 0.000001);

    BOOST_CHECK_EQUAL(box.nCell[0], 0);
    BOOST_CHECK_EQUAL(box.nCell[1], 0);
    BOOST_CHECK_EQUAL(box.nCell[2], 0);
    BOOST_CHECK_EQUAL(box.nCell[3], 0);

    BOOST_REQUIRE_CLOSE(box.defaultBoxCell, 0, 0.000001);
    BOOST_REQUIRE_CLOSE(box.maxScale, 0, 0.000001);
    BOOST_CHECK_EQUAL(10, box.nobjects);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test initBox
BOOST_AUTO_TEST_CASE(update_box_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: update box";

    e.build2PFC("AUTO_1D");

    BoxType box;
    le3gc::dVector boxmin;
    le3gc::dVector boxmax;

    boxmin[0] = boxmin[1] = boxmin[2] = 1;
    boxmax[0] = boxmax[1] = boxmax[2] = 2;

    // get scale along 1D axis
    box = e.tpcf.boxFromVector(boxmin,boxmax,15);

    // get scale along 1D axis
    e.tpcf.updateBox(box);

    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.posMax[0], 2.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.posMax[1], 2.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.posMax[2], 2.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.posMin[0], 1.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.posMin[1], 1.0, 0.000001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_box.posMin[2], 1.0, 0.000001);
    BOOST_CHECK_EQUAL(e.tpcf.m_box.nobjects, 15);

    e.clean();
}

// -------------------------------------------------------------------------- //
//                            Private Methods
// -------------------------------------------------------------------------- //

// test get parameters NONE configuration (AUTO_1D)
BOOST_AUTO_TEST_CASE(get_parameters_NONE_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get parameters NONE";

    // default parameters: NO statistics --> default AUTO_1D
    Parameters params = e.setupParameters("NONE");
    e.tpcf.m_getParameters(params);

    BOOST_CHECK(CorrelationType::NONE == e.tpcf.m_statistics);
    // 1D
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, e.n1);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_min1D, e.min1, 00001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_max1D, e.max1, 00001);

    // 2D is disabled
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin2D, 1);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_min2D, 0.0, 00001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_max2D, 0.0, 00001);

    // pi disabled
    BOOST_REQUIRE_CLOSE(e.tpcf.m_piMax, 0.0, 00001);

    BOOST_CHECK(true == e.tpcf.m_useWeight);
    BOOST_CHECK(false == e.tpcf.m_useTXT);
}

// -------------------------------------------------------------------------- //

// test get parameters AUTO_1D configuration
BOOST_AUTO_TEST_CASE( get_parameters_AUTO_1D_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get parameters AUTO_1D";

    // test AUTO_1D
    Parameters params = e.setupParameters("AUTO_1D");
    e.tpcf.m_getParameters(params);

    BOOST_CHECK(CorrelationType::AUTO_1D == e.tpcf.m_statistics);
    // 1D
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, e.n1);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_min1D, e.min1, 00001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_max1D, e.max1, 00001);

    // 2D is disabled
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin2D, 1);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_min2D, 0.0, 00001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_max2D, 0.0, 00001);

    // pi disabled
    BOOST_REQUIRE_CLOSE(e.tpcf.m_piMax, 0.0, 00001);

    BOOST_CHECK(true == e.tpcf.m_useWeight);
    BOOST_CHECK(false == e.tpcf.m_useTXT);
}

// -------------------------------------------------------------------------- //


// test get parameters AUTO_2DCart configuration + USE WEIGHT false
BOOST_AUTO_TEST_CASE( get_parameters_AUTO_2DCart_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get parameters AUTO_2DCart";

    // test AUTO_2DCart using global parameters
    Parameters params = e.setupParameters("AUTO_2DCART");
    e.tpcf.m_getParameters(params);

    BOOST_CHECK(CorrelationType::AUTO_2DCART == e.tpcf.m_statistics);
    // 1D
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, e.n1);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_min1D, e.min1, 00001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_max1D, e.max1, 00001);

    // 2D
    BOOST_CHECK_EQUAL(e.tpcf.m_nBin2D, e.n2);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_min2D, e.min2, 00001);
    BOOST_REQUIRE_CLOSE(e.tpcf.m_max2D, e.max2, 00001);

    // pi disabled
    BOOST_REQUIRE_CLOSE(e.tpcf.m_piMax, e.max2, 00001);

    BOOST_CHECK(true == e.tpcf.m_useWeight);
    BOOST_CHECK(false == e.tpcf.m_useTXT);
}

// -------------------------------------------------------------------------- //

// test get parameters AUTO_2DPol configuration
BOOST_AUTO_TEST_CASE(get_parameters_AUTO_2DPol_test) {

  logger.info();
  logger.info() << "--> LINKEDLIST: get parameters AUTO_2DPOL";
  
  // test AUTO_2DPol using global parameters
  Parameters params = e.setupParameters("AUTO_2DPOL");
  e.tpcf.m_getParameters(params);
  
  BOOST_CHECK(CorrelationType::AUTO_2DPOL == e.tpcf.m_statistics);
  // 1D
  BOOST_CHECK_EQUAL(e.tpcf.m_nBin1D, e.n1);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_min1D, e.min1, 00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_max1D, e.max1, 00001);

  // 2D
  BOOST_CHECK_EQUAL(e.tpcf.m_nBin2D, e.n2);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_min2D, -1, 00001);
  BOOST_REQUIRE_CLOSE(e.tpcf.m_max2D,  1, 00001);

  // pi
  BOOST_REQUIRE_CLOSE(e.tpcf.m_piMax, e.max2, 00001);
  
  BOOST_CHECK(true == e.tpcf.m_useWeight);
  BOOST_CHECK(false == e.tpcf.m_useTXT);
}

// -------------------------------------------------------------------------- //

// test statistics
BOOST_AUTO_TEST_CASE( check_correlation_type_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get statistics";

    // no matter the 2PCF type currently loaded

    // test one_dim name
    BOOST_CHECK (CorrelationType::AUTO_1D == e.tpcf.m_getStatistics("Auto_1d"));

    // test two_dim_polar name
    BOOST_CHECK (CorrelationType::AUTO_2DPOL == e.tpcf.m_getStatistics("Auto_2dpol"));

    // test two_dim_cart name
    BOOST_CHECK (CorrelationType::AUTO_2DCART == e.tpcf.m_getStatistics("AUTo_2DCart"));

      // test one_dim name
    BOOST_CHECK (CorrelationType::CROSS_1D == e.tpcf.m_getStatistics("Cross_1d"));

    // test two_dim_polar name
    BOOST_CHECK (CorrelationType::CROSS_2DPOL == e.tpcf.m_getStatistics("CrosS_2dpol"));

    // test two_dim_cart name
    BOOST_CHECK (CorrelationType::CROSS_2DCART == e.tpcf.m_getStatistics("CROSS_2DCart"));

    // test NONE
    BOOST_CHECK (CorrelationType::NONE == e.tpcf.m_getStatistics("NonE"));
}

// -------------------------------------------------------------------------- //

  // test binning scale
BOOST_AUTO_TEST_CASE(check_binning_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale type";

    // no matter the 2PCF type currently loaded

    // check linear
    BOOST_CHECK (BinningType::LIN == e.tpcf.m_getScaleType("LiN"));

    // check logarithminc
    BOOST_CHECK (BinningType::LOG == e.tpcf.m_getScaleType("lOg"));

    // NOTE: test to be removed if NO DEFAULT
    // test default binning for unknown input
    BOOST_CHECK_THROW (e.tpcf.m_getScaleType("WhatEVER"), std::exception);
}

// -------------------------------------------------------------------------- //

  // test get scales with statistics NONE
BOOST_AUTO_TEST_CASE(check_scales_NONE_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits NONE";

    // reset 2PCF to NONE
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::NONE;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;

    e.tpcf.m_min2D = 1.0;
    e.tpcf.m_max2D = 1.0;
    e.tpcf.m_scale2D = le3gc::BinningType::NONE;
    // values 2D set to 0 by default
    ScalesType test = e.tpcf.m_getScales();

    // check nothig has been setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 0.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test get scales with statistics AUTO_1D
BOOST_AUTO_TEST_CASE( check_scales_AUTO_1D_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits AUTO_1D";

    // reset 2PCF to AUTO_1D
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::AUTO_1D;

    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;
    // set values 2D that should be ignored
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LIN;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 2.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 0.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //


BOOST_AUTO_TEST_CASE( check_scales_AUTO_2DCart_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits AUTO_2DCart";

    // reset 2PCF to AUTO_2DCart
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::AUTO_2DCART;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LIN;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 2.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 3.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 6.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //


BOOST_AUTO_TEST_CASE( check_scales_AUTO_2DPol_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits AUTO_2DPol";

    // reset 2PCF to AUTO_2DPol
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::AUTO_2DPOL;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LIN;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 2.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 3.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 6.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);
    
    e.clean();
}
// -------------------------------------------------------------------------- //

// test get scales with statistics AUTO_1D
BOOST_AUTO_TEST_CASE( check_scales_CROSS_1D_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits CROSS_1D";

    // reset 2PCF to AUTO_1D
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::CROSS_1D;

    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;
    // set values 2D that should be ignored
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LIN;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 2.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 0.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //


BOOST_AUTO_TEST_CASE( check_scales_CROSS_2DCart_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits CROSS_2DCart";

    // reset 2PCF to AUTO_2DCart
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::CROSS_2DCART;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LIN;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 2.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 3.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 6.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //


BOOST_AUTO_TEST_CASE( check_scales_CROSS_2DPol_test ) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits CROSS_2DPol";

    // reset 2PCF to AUTO_2DPol
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::CROSS_2DPOL;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LIN;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LIN;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 2.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 3.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 6.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);
    
    e.clean();
}

// -------------------------------------------------------------------------- //

// test log scale set 1D
BOOST_AUTO_TEST_CASE(check_scales_1D_log_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits LOG scale 1D";

    // reset 2PCF to AUTO_1D with LOG scales
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::AUTO_1D;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LOG;
    // set values 2D that should be ignored
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LOG;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted: only 1D should be setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.3010299956639812, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.6020599913279624, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 0.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test log scale set 2D
BOOST_AUTO_TEST_CASE(check_scales_2DP_log_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits LOG scale 2DP";

    // reset 2PCF to AUTO_2DPol with LOG scales
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::AUTO_2DPOL;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LOG;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LOG;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.3010299956639812, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.6020599913279624, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.47712125471966244, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.7781512503836436, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test log scale set 2D
BOOST_AUTO_TEST_CASE(check_scales_2D_log_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits LOG scale 2D";

    // reset 2PCF to AUTO_2DPol with LOG scales
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::AUTO_2DCART;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LOG;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LOG;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.3010299956639812, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.6020599913279624, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.47712125471966244, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.7781512503836436, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test log scale set 1D
BOOST_AUTO_TEST_CASE(check_scales_cross_1D_log_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits LOG scale 1D";

    // reset 2PCF to CROSS_1D with LOG scales
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::CROSS_1D;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LOG;
    // set values 2D that should be ignored
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LOG;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted: only 1D should be setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.3010299956639812, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.6020599913279624, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 0.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 0.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test log scale set 2D
BOOST_AUTO_TEST_CASE(check_scales_cross_2DP_log_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits LOG scale 2DP";

    // reset 2PCF to CROSS_2DPol with LOG scales
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::CROSS_2DPOL;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LOG;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LOG;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.3010299956639812, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.6020599913279624, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.47712125471966244, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.7781512503836436, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test log scale set 2D
BOOST_AUTO_TEST_CASE(check_scales_cross_2D_log_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get scale limits LOG scale 2D";

    // reset 2PCF to CROSS_2DPol with LOG scales
    e.tpcf.m_statistics = le3gc::LE3_GC_2PCF::CorrelationType::CROSS_2DCART;
    // set values 1D
    e.tpcf.m_min1D = 2.0;
    e.tpcf.m_max1D = 4.0;
    e.tpcf.m_scale1D = le3gc::BinningType::LOG;
    // set values 2D
    e.tpcf.m_min2D = 3.0;
    e.tpcf.m_max2D = 6.0;
    e.tpcf.m_scale2D = le3gc::BinningType::LOG;

    ScalesType test = e.tpcf.m_getScales();

    // check values setted
    BOOST_REQUIRE_CLOSE(test.min1D, 0.3010299956639812, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1D, 0.6020599913279624, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min1Dsq, 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max1Dsq, 16.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2D, 0.47712125471966244, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2D, 0.7781512503836436, 0.00001);
    BOOST_REQUIRE_CLOSE(test.min2Dsq, 9.0, 0.00001);
    BOOST_REQUIRE_CLOSE(test.max2Dsq, 36.0, 0.00001);

    e.clean();
}

// -------------------------------------------------------------------------- //

// test boxSize detection
BOOST_AUTO_TEST_CASE(check_boxSize_test) {

    logger.info();
    logger.info() << "--> LINKEDLIST: get box size";

    e.build2PFC("AUTO_1D");
    e.loadTestDataCat();
    
    BoxType box = e.tpcf.m_getBoxSize(e.datacat);

    le3gc::dVector result = box.posMax - box.posMin;

    BOOST_REQUIRE_CLOSE(result[0], 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(result[1], 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(result[2], 4.0, 0.00001);
    BOOST_REQUIRE_CLOSE(result[3], 0.0, 0.00001);
    BOOST_CHECK_EQUAL(box.nobjects, 103);
}

// -------------------------------------------------------------------------- //

// test update total pairs
BOOST_AUTO_TEST_CASE(update_total_pairs) {

    logger.info();
    logger.info() << "--> LINKEDLIST: update_total_pairs";

    e.loadTestDataCat();
    e.loadTestRandCat();

    double pairs;

    // case without weight
    e.tpcf.m_useWeight = false;

    pairs = 1;
    e.tpcf.updateTotalPairs(e.datacat,e.randcat,pairs,true);
    BOOST_REQUIRE_CLOSE(pairs, 1 + e.datacat.size()*e.randcat.size(), 0.00001);

    pairs = 2;
    e.tpcf.updateTotalPairs(e.datacat,e.randcat,pairs,false);
    BOOST_REQUIRE_CLOSE(pairs, 2 + e.datacat.size()*(e.datacat.size()-1)/2, 0.00001);

    // case with weight
    e.tpcf.m_useWeight = true;

    double npd = e.datacat.size()-1;
    double npr = e.randcat.size()-1;

    pairs = 1;
    e.tpcf.updateTotalPairs(e.datacat,e.randcat,pairs,true);
    BOOST_REQUIRE_CLOSE(pairs, 1 + npd*npr, 0.00001);

    pairs = 2;
    e.tpcf.updateTotalPairs(e.datacat,e.randcat,pairs,false);
    BOOST_REQUIRE_CLOSE(pairs, 2 + npd*(npd-1)/2, 0.00001);
}

// -------------------------------------------------------------------------- //

// test update total pairs
BOOST_AUTO_TEST_CASE(fill_pair_counts) {

    logger.info();
    logger.info() << "--> LINKEDLIST: fill pair counts";

    e.build2PFC("AUTO_1D");

    e.loadTestDataCat();
    e.loadTestRandCat();

    size_t bins = e.tpcf.m_nBin1D;

    // correlation vector
    vector<double> pairs(bins, 0.0);

    // case without weight
    e.tpcf.fillPairCounts(e.datacat[0], e.randcat[0], pairs, 0.0);

    vector<double> expected(pairs.size(), 0.0);
    expected[0] = 1;
    BOOST_CHECK_EQUAL_COLLECTIONS(pairs.begin(), pairs.end(), expected.begin(), expected.end());

    // case without weight
    e.tpcf.fillPairCounts(e.datacat[0], e.randcat[0], pairs, 1.0);

    expected[0] = 2;
    BOOST_CHECK_EQUAL_COLLECTIONS(pairs.begin(), pairs.end(), expected.begin(), expected.end());

    e.clean();
}
// -------------------------------------------------------------------------- //

// check correlation product for AUTO_1D - DEFAULT
// BOOST_AUTO_TEST_CASE(test_correlation_product) {
// 
//     logger.info();
//     logger.info() << "--> write correlation fits products";
// 
//     // reset to AUTO_1D
//     e.build2PFC("AUTO_1D");
// 
//     // IO Constructor
//     le3gc::LE3_GC_2PCF::ProductsIO io = e.buildProducts(e.rootdir,"EUCLID");
// 
//     // define and read parameter file
//     le3gc::Parameters params(e.parFile);
// 
//     // set input & output
//     io.setInputOutput(params);
// 
//     // dummy correlation vector
//     vector<double> vec(e.tpcf.m_nBin1D, 127.0);
// 
//     // write correlation
//     e.tpcf.writeCorrelation(e.tmpCorrName, "", vec, 
// 			    e.datacat, e.randcat, e.datacat, e.randcat, e.datacat, e.randcat, cosmo);
// 
//     // write pairs
//     e.tpcf.writePairs(e.tmpPairName, vec, vec, vec, vec, 1.0, 1.0, 1.0 , 1.0,
//               e.datacat, e.datacat, e.datacat, e.datacat, cosmo);
// 
//     // write product
//     io.writeCorrProduct(e.tmpPairName,e.tmpCorrName,"","test.xml");
//     
//     logger.info();
//     std::string prefix = "DpdLE3gcTwoPointAutoCorr";
//     std::string node = prefix + ".Data.DataCatalog";
//     std::string Name = le3gc::ByendingsDm::pickNodeValue<std::string>("test.xml", node);
//     BOOST_CHECK_EQUAL("test_txt.txt", Name);
//     
//     le3gc::ByendingsDm prod("2PCF");
//     prod.loadXMLfile("test.xml");
// 
//     std::string name = "tw:DpdLE3gcTwoPointAutoCorr";
//     BOOST_CHECK(true == prod.keyExists(name + ".Header.ProductId"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Header.PlanId"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Header.PPOId"));
// 
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.DataCatalog"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.RandCatalog"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.CountingMethod"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinType"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinNumber"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinMinValue"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinMaxValue"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.UseWeight"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.SplitFactor"));
//     //BOOST_CHECK(true == prod.keyExists(name + ".Data.CosmologyID"));
//     //BOOST_CHECK(true == prod.keyExists(name + ".Data.ReleaseName"));
// 
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.PairsFile.DataContainer.FileName"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.CorrelationFile.DataContainer.FileName"));
// 
//     e.clean();
// }

// -------------------------------------------------------------------------- //

// check correlation product for AUTO_2DCart - DEFAULT
// BOOST_AUTO_TEST_CASE(test_correlation_2DCART_product) {
// 
//     logger.info();
//     logger.info() << "--> write correlation AUTO_2DCART its products";
// 
//     // reset to AUTO_2D
//     e.build2PFC("AUTO_2DCART");
// 
//     // IO Constructor
//     le3gc::LE3_GC_2PCF::ProductsIO io = e.buildProducts(e.rootdir,"EUCLID");
// 
//     // define and read parameter file
//     le3gc::Parameters params(e.parFile);
// 
//     // set input & output
//     io.setInputOutput(params);
// 
//     // dummy correlation vector
//     vector<double> vec(1+e.tpcf.m_nBin1D*e.tpcf.m_nBin2D, 127.0);
// 
//     // write correlation
//     e.tpcf.writeCorrelation(e.tmpCorr2Name, e.tmpCorrName, vec, 
// 			    e.datacat, e.randcat, e.datacat, e.randcat, e.datacat, e.randcat, cosmo);
// 
//     // write pairs
//     e.tpcf.writePairs(e.tmpPairName, vec, vec, vec, vec, 1.0, 1.0, 1.0 , 1.0,
//               e.datacat, e.datacat, e.datacat, e.datacat, cosmo);
// 
//     // write product
//     io.writeCorrProduct(e.tmpPairName,e.tmpCorrName,e.tmpCorr2Name,"test2.xml");
// 
//     le3gc::ByendingsDm prod("2PCF");
//     prod.loadXMLfile("test2.xml");
// 
//     std::string name = "tw:DpdLE3gcTwoPointAutoCorrCart";
//     BOOST_CHECK(true == prod.keyExists(name + ".Header.ProductId"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Header.PlanId"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Header.PPOId"));
// 
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.DataCatalog"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.RandCatalog"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.CountingMethod"));
// 
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinTypeDim1"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinNumberDim1"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinMinValueDim1"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinMaxValueDim1"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinTypeDim2"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinNumberDim2"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinMinValueDim2"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.BinMaxValueDim2"));
// 
// 
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.UseWeight"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.SplitFactor"));
//     //BOOST_CHECK(true == prod.keyExists(name + ".Data.CosmologyID"));
//     //BOOST_CHECK(true == prod.keyExists(name + ".Data.ReleaseName"));
// 
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.PairsFile.DataContainer.FileName"));
//     BOOST_CHECK(true == prod.keyExists(name + ".Data.CorrelationFile.DataContainer.FileName"));
// 
//     e.clean();
// }

//-----------------------------------------------------------------------------

BOOST_AUTO_TEST_SUITE_END ()
