/**
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      LE3_GC_2PCF/LinkedList.h
 * @date      01/01/18
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> de <PERSON> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#ifndef _LE3_GC_2PCF_LINKEDLIST_H
#define _LE3_GC_2PCF_LINKEDLIST_H

#include "LE3_GC_2PCF/TwoPointCorrelation.h"

namespace le3gc {
namespace LE3_GC_2PCF {

  /**
   * @class     LinkedList
   * @brief     The two-point correlation function using a linked-list structure
   * @details   The two-point correlation pairs are counted using a linked-list
   *            structure to select objects in the box volume
   * @version   4.1
   */
  class LinkedList : public TwoPointCorrelation {

  public:

    /**
     * @brief   Default empty constructor
     * @return  Empty LinkedList two-point-correlation object
     */
    LinkedList();

    /**
     * @brief   Constructor
     * @param   param <Parameters> object
     * @return  Configured LinkedList object
     * @detail  The "param" object is checked for errors in the configuration
     */
    explicit LinkedList(const Parameters& param);

    /**
     * @brief   Set up 2PCF
     * @detail  Sets maximum and minimum scales
     * @return  None
     */
    void SetupPairCount() override;

    /**
     * @brief   Create structure to count pairs for catalog
     * @detail  The catalog object are organized into cubic cells for a chain-mesh
     *          partitioning structure
     * @warn    Catalog is modified (sorted)
     * @param   cat, <Catalog> object
     * @param   catType, <InputCatType> extended catalogue type
     * @return  None
     */
    void Indicize(le3gc::Catalog& cat, InputCatType catType) override;

    /**
     * @brief   Count pairs between catalogs
     * @details The pairs are counted by comparing all the objects in the same cell
     *          against the surrounding cells.
     * @param   cat1, <Catalog> object
     * @param   cat2, <Catalog> object
     * @param   catType1, <inputCatType> catalog type
     * @param   catType2, <InputCatType> catalog type
     * @param   pairCount, <vector<double> pair counts vector
     * @param   pairMax, <double> maximum pair count   
     */
    void countPairs(le3gc::Catalog& cat1, le3gc::Catalog& cat2,
                    InputCatType catType1, InputCatType catType2,
                    std::vector<double> &pairCount,
                    double &pairMax, bool allPairs) override;

    /**
     * @brief   Clear data structure
     * @detail  Clear data structure
     * @return  None
     */
    void clearDataStruct(InputCatType catType) override;

    /**
     * @brief   Default destructor
     */
    ~LinkedList() {};

  private:

    /// Linked-list-like catalog organization structure for data catalog
    std::vector<size_t> m_linkData1;
    std::vector<size_t> m_linkData2;

    /// Linked-list-like catalog organization structure for random catalog
    std::vector<size_t> m_linkRand1;
    std::vector<size_t> m_linkRand2;

    /// Linked-list-like catalog organization structure for reconstructed catalog
    std::vector<size_t> m_linkRec1;
    std::vector<size_t> m_linkRec2;

    /**
     * @brief   Get the LinkedList
     *
     * @detail Function to get the linked list structure, that is the
     * structure containing organized objects
     *
     * @param type, <CatalogType> object
     *
     * @return pointer to structure containing organized objects
     */
    std::vector<size_t>& GetLinkedList(const InputCatType& type);

  }; /* End of LinkedList class */
} /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */


#endif
