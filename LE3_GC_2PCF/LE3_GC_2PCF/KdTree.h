/**
 * Copyright (C) 2012-2024 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      LE3_GC_2PCF/KdTree.h
 * @date      01/01/18
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> de <PERSON> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#ifndef _LE3_GC_2PCF_KDTREE_H
#define _LE3_GC_2PCF_KDTREE_H

#include "LE3_GC_2PCF/TwoPointCorrelation.h"

#include <memory>
#include <chrono>
#include <unordered_map>

#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"

using std::shared_ptr;

namespace le3gc {
  namespace LE3_GC_2PCF {
    
    /**
     * @structure Node
     * @brief     Defines the kd-tree structure
     * @details   The kd-tree structure is constructed from connected 
     *            nodes that keep internal spatial information 
     *
     */
    typedef struct Node {
      size_t first;
      size_t last;
      int splitDim;
      uint32_t locCode;
      std::vector<double> pMin;
      std::vector<double> pMax;
      bool hasChildren;
      
    Node(size_t f,
         size_t l,
         int sp,
         uint32_t lc,
	 std::vector<double>& min,
         std::vector<double>& max,
         bool ch)
    : first(f), last(l), splitDim(sp), locCode(lc), pMin(min), pMax(max), hasChildren(ch){};
    } Node;
    
    /**
     * @class KdTree
     * @brief     The two-point correlation function using a kdTree structure
     * @details   The two-point correlation pairs are counted using a kdTree
     *            structure to select near objects in the box volume
     * @version   4.1
     */
    class KdTree : public TwoPointCorrelation {
      
    public:
      
      /**
       * @brief   Default empty constructor
       * @return  Empty KdTree two-point-correlation object
       */
      KdTree();
      
      /**
       * @brief   Constructor
       * @param   param <Parameters> object
       * @return  Configured KdTree object
       * @detail  The "param" object is checked for errors in the configuration
       */
      explicit KdTree(const le3gc::Parameters& param);
      
      /**
       * @brief   Set up 2PCF
       * @detail  Sets maximum and minimum scales
       * @return  None
       */
      void SetupPairCount() override;
      
      /**
       * @brief   Create structure to count pairs for catalog
       * @detail  The catalog object are organized into a tree structure by ordering
       *          the catalog objects into x, y, z directions and splitting into nodes
       *          at half distance
       * @warn    Catalog is modified (sorted)
       * @param   cat, <Catalog> object
       * @param   catType <InputCatType> extended catalog type
       * @return  None
       */
      void Indicize(le3gc::Catalog& cat, InputCatType catType) override;
      
      /**
       * @brief   Count pairs between catalogs
       * @details The counting structure depends on the implementation
       * @param   cat1, <Catalog> object
       * @param   cat2, <Catalog> object
       * @param   catType1, catalog type
       * @param   catType2, catalog type
       * @param   pairCount vector<double>, pair counts vector
       * @param   pairMax <double>, maximum pairs
       * @return  None
       */
      void countPairs(le3gc::Catalog& cat1, le3gc::Catalog& cat2,
		      InputCatType catType1, InputCatType catType2,
		      std::vector<double> &pairCount,
		      double &pairMax, bool allPairs) override;
      
      /**
       * @brief   Clear data structure
       * @detail  Clear data structure
       * @return  None
       */
      void clearDataStruct(InputCatType catType) override;

      /**
       * @brief Destructor
       */
      ~KdTree() {};

    private:
      
      // maximum number of objects per tree node
      size_t m_nMax;

      // maximum number of objects per tree node
      int m_maxDepth;
      
      // KdTree catalog organization structure for data catalog
      std::unordered_map<uint32_t, shared_ptr<Node>> m_treeData1;
      std::unordered_map<uint32_t, shared_ptr<Node>> m_treeData2;
      
      // KdTree catalog organization structure for random catalog
      std::unordered_map<uint32_t, shared_ptr<Node>> m_treeRand1;
      std::unordered_map<uint32_t, shared_ptr<Node>> m_treeRand2;
      
      // KdTree catalog organization structure for reconstructed catalog
      std::unordered_map<uint32_t, shared_ptr<Node>> m_treeRec1;
      std::unordered_map<uint32_t, shared_ptr<Node>> m_treeRec2;
      
      /**
       * @brief   select the correct data structure for the provided catalog
       * @param   InputCatType, <type> catalog type
       * @return  std::shared_ptr<Node>, pointer to node
       */
      std::unordered_map<uint32_t, shared_ptr<Node>>& m_getTree(const InputCatType& catType);
      
      /**
       * @brief   function to create tree node recursively 
       * @param   cat, <Catalog> catalog
       * @return  None
       */
      void m_createTree(le3gc::Catalog& cat,
			size_t first,
			size_t last,
			int splitDim,
			uint32_t locCode,
			std::vector<double>& pMin,
			std::vector<double>& pMax,
			InputCatType catType);

      /**
       * @brief   Point to node 
       * @param   locCode, <uint> locational code of the node
       */
      shared_ptr<Node> m_lookupNode(uint32_t locCode, InputCatType catType);

      /**
       * @brief   Create node list for parallel KD_TREE method
       * @param   node, <shared_ptr> to tree structure node origin
       * @param   depthRemain, <int> tree depth limit to add nodes
       * @param   nodeList, <vector<shared_ptr<Node>>> node list
       * @return  None
       */
      void m_addNodeList(std::shared_ptr<Node> node,
			 int depthRemain,
			 std::vector<std::shared_ptr<Node>>& nodeList,
			 InputCatType catType);

      /**
       * @brief   Compute min/max pair distance between two nodes
       * @param   node1, <shared_ptr<ocNode>> to tree node 1
       * @param   node2, <shared_ptr<ocNode>> to tree node 2
       * @param   min, <double> min distance
       * @param   max, <double> max distance
       * @return  None
       */
      void m_distNodes(const shared_ptr<Node> node1, const shared_ptr<Node> node2, double& min, double& max);

      /**
       * @brief   Parallel recursive pair counting along the tree
       * @param   node1, pointer to tree node of catalog 1 where to count pairs
       * @param   node2, pointer to tree node of catalog 2 where to count pairs
       * @param   cat1, <Catalog> object
       * @param   cat2, <Catalog> object
       * @param   pairCounts, <vector<double>> counted pairs
       * @param   allPairs, <bool> flag to count pairs between different catalogs
       * @return  None
       */
      void m_countPairsSerial(shared_ptr<Node> node1,
			      shared_ptr<Node> node2,
			      Catalog& cat1,
			      Catalog& cat2,
			      std::vector<double>& pairCounts,
			      bool allPairs,
			      InputCatType catType1, 
			      InputCatType catType2);
      
    }; /* end of KdTree class */    
  } /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */

#endif
