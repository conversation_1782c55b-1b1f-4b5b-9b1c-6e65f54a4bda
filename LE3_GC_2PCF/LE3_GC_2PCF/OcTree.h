/**
 * Copyright (C) 2012-2024 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      LE3_GC_2PCF/OctTree.h
 * @date      19/02/24
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @copyright (C) 2012-2024 Euclid Science Ground Segment - GNU General Public License
 */

#ifndef _LE3_GC_2PCF_OCTREE_H
#define _LE3_GC_2PCF_OCTREE_H

#include "LE3_GC_2PCF/TwoPointCorrelation.h"

#include <memory>
#include <chrono>
#include <unordered_map>

#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"

using std::shared_ptr;

namespace le3gc {
  namespace LE3_GC_2PCF {
    
    /**
     * @structure ocNode
     * @brief     Defines the oc-tree structure
     * @details   The octree structure is constructed from connected 
     *            nodes that keep internal spatial information 
     */
    struct ocNode {
      uint32_t locCode;
      uint8_t childExists;
      size_t first;
      size_t last;
      uint32_t depth;
      
    ocNode(uint32_t co, uint8_t ce, size_t fi, size_t la) : locCode(co), childExists(ce), first(fi), last(la)
      { depth = (31-__builtin_clz(co))/3; };

    };
    
    /**
     * @class OcTree
     * @brief     The two-point correlation function using a octree structure
     * @details   The two-point correlation pairs are counted using a octree
     *            structure to select near objects in the box volume
     * @version   1.0
     */
    class OcTree : public TwoPointCorrelation {
      
    public:
      
      /**
       * @brief   Default empty constructor
       * @return  Empty OcTree two-point-correlation object
       */
      OcTree();

      /**
       * @brief   Constructor
       * @param   param <Parameters> object
       * @return  Configured octree object
       * @detail  The "param" object is checked for errors in the configuration
       */
      explicit OcTree(const le3gc::Parameters& param);

      /**
       * @brief   Set up 2PCF
       * @detail  Sets maximum and minimum scales
       * @return  None
       */
      void SetupPairCount() override;

      /**
       * @brief   Create structure to count pairs for catalog
       * @detail  The catalog object are organized into a tree structure by ordering
       *          the catalog objects into x, y, z directions and splitting into nodes
       *          at half distance
       * @warn    Catalog is modified (sorted)
       * @param   cat, <Catalog> object
       * @param   catType <InputCatType> extended catalog type
       * @return  None
       */
      void Indicize(le3gc::Catalog& cat, InputCatType catType) override;

      /**
       * @brief   Count pairs between catalogs
       * @details The counting structure depends on the implementation
       * @param   cat1, <Catalog> object
       * @param   cat2, <Catalog> object
       * @param   catType1, catalog type
       * @param   catType2, catalog type
       * @param   pairCount vector<double>, pair counts vector
       * @param   pairMax <double>, maximum pairs
       * @return  None
       */
      void countPairs(le3gc::Catalog& cat1, le3gc::Catalog& cat2,
		      InputCatType catType1, InputCatType catType2,
		      std::vector<double> &pairCount,
		      double &pairMax, bool allPairs) override;

      /**
       * @brief   Clear data structure
       * @detail  Clear data structure
       * @return  None
       */
      void clearDataStruct(InputCatType catType) override;

      /**
       * @brief Destructor
       */
      ~OcTree() {};

    private:

      // maximum number of objects per tree node
      size_t m_nMax;

      // maximum vertex size at depth 0
      double m_size = -1;
      
      // origin of the tree root
      std::vector<double> m_origin;

      // squared number of ells per depth
      double m_sqrCellSize[11];

      // maximum depth
      uint32_t m_maxDepth;

      // OcTree catalog organization structure for data catalog
      std::unordered_map<uint32_t, shared_ptr<ocNode>> m_treeData1;
      std::unordered_map<uint32_t, shared_ptr<ocNode>> m_treeData2;
      
      // OcTree catalog organization structure for random catalog
      std::unordered_map<uint32_t, shared_ptr<ocNode>> m_treeRand1;
      std::unordered_map<uint32_t, shared_ptr<ocNode>> m_treeRand2;
      
      // OcTree catalog organization structure for reconstructed catalog
      std::unordered_map<uint32_t, shared_ptr<ocNode>> m_treeRec1;
      std::unordered_map<uint32_t, shared_ptr<ocNode>> m_treeRec2;

      // x,y,z masks for bitwise Morton code operations
      static const uint32_t _Z_MASK = 0x24924924;  // 0b...00100100
      static const uint32_t _Y_MASK = 0x92492492;  // 0b...10010010
      static const uint32_t _X_MASK = 0x49249249;  // 0b...01001001
      
      void m_createTree(Catalog& cat, size_t first, size_t last, uint32_t locCode, InputCatType catType);

      /**
       * @brief   select the correct data structure for the provided catalog
       * @param   InputCatType, <type> catalog type
       * @return  <std::unordered_map<uint32_t, ocNode>, hash table
       */
      std::unordered_map<uint32_t, shared_ptr<ocNode>>& m_getTree(const InputCatType& type);
      
      /**
       * @brief   Compactify interlaced 32bits bitset
       * @param   x, <uint> bitset to be compactified
       * @return  <uint>
       */
      uint32_t m_compact1By2(uint32_t x);

      /**
       * @brief   Point to node 
       * @param   locCode, <uint> locational code of the node
       */
      shared_ptr<ocNode> m_lookupNode(uint32_t locCode, InputCatType catType);

      /**
       * @brief   Create node list for parallel octree method
       * @param   node, <shared_ptr<ocNode>> to tree node
       * @param   nodeList, <vector<shared_ptr<Node>>> node list
       * @return  None
       */
      void m_addNodeList(shared_ptr<ocNode> node, int depthRemain, std::vector<shared_ptr<ocNode>>& nodeList, InputCatType catType);
      
       /**
       * @brief   Compute min/max pair distance between two nodes
       * @param   node1, <shared_ptr<ocNode>> to tree node 1
       * @param   node2, <shared_ptr<ocNode>> to tree node 2
       * @param   min, <double> min distance
       * @param   max, <double> max distance
       * @return  None
       */
      void m_distNodes(const shared_ptr<ocNode> node1, const shared_ptr<ocNode> node2, double& min, double& max);
      
      /**
       * @brief   Parallel recursive pair counting along the tree
       * @param   node1, pointer to tree node of catalog 1 where to count pairs
       * @param   node2, pointer to tree node of catalog 2 where to count pairs
       * @param   cat1, <Catalog> object
       * @param   cat2, <Catalog> object
       * @param   pairCounts, <vector<double>> counted pairs
       * @param   allPairs, <bool> flag to count pairs in the case data-random catalogs
       * @return  None
       */
      void m_countPairsSerial(shared_ptr<ocNode> node1, shared_ptr<ocNode> node2,
			      le3gc::Catalog& cat1, le3gc::Catalog& cat2,
			      std::vector<double>& pairCounts,
			      bool allPairs,
			      InputCatType catType1, InputCatType catType2);
      
    }; /* end of OcTree class */
  } /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */

#endif
