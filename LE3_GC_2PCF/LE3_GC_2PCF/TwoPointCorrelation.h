/**
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      LE3_GC_2PCF/TwoPointCorrelation.h
 * @date      01/01/18
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#ifndef _LE3_GC_2PCF_TWOPOINTCORRELATION_H
#define _LE3_GC_2PCF_TWOPOINTCORRELATION_H

#include <vector>
#include <string>
#include <boost/bimap.hpp>

#include "ElementsKernel/ProgramHeaders.h"

#include "LE3_GC_Libraries/TypeDef.h"
#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"

namespace le3gc {class Fits;}
namespace le3gc {class Galaxy;}
namespace le3gc {class Catalog;}
namespace le3gc {class Parameters;}
namespace le3gc {class Cosmology;}

namespace le3gc {
namespace LE3_GC_2PCF {

  /**
   * @enum      CorrelationType
   * @brief     Type of correlation function to compute
   *
   * @var       AUTO_1D
   *            ...
   * @var       AUTO_2DCart
   *            ...
   * @var       AUTO_2DPol
   *            ...
   * @var       CROSS_1D
   *            ...
   * @var       CROSS_2DCart
   *            ...
   * @var       CROSS_2DPol
   *            ...
   * @var       AUTO_REC_1D
   *            ...
   * @var       AUTO_REC_2DCart
   *            ...
   * @var       AUTO_REC_2DPol
   *            ...
   * @var       CROSS_REC_1D
   *            ...
   * @var       CROSS_REC_2DCart
   *            ...
   * @var       CROSS_REC_2DPol
   *            ...
   * @var       NONE
   *            ...
   */
  enum class CorrelationType {AUTO_1D, AUTO_2DCART, AUTO_2DPOL, 
      CROSS_1D, CROSS_2DCART, CROSS_2DPOL,
      AUTO_REC_1D, AUTO_REC_2DCART, AUTO_REC_2DPOL, 
      CROSS_REC_1D, CROSS_REC_2DCART, CROSS_REC_2DPOL,NONE};


  /**
   * @enum      AxisType
   * @brief     Binning dimensions
   *
   * @var       DIM1
   *            ...
   * @var       DIM2
   *            ...
   */
  enum class AxisType {DIM1, DIM2};

  /**
   * @enum      InputCatType 
   * @brief     extended catalogue type 
   *
   * @var       DATA    
   *            ...
   * @var       DATA1
   *            ...
   * @var       DATA2
   *
   * @var       RAND    
   *            ...
   * @var       RAND1
   *            ...
   * @var       RAND2
   *
   * @var       REC    
   *            ...
   * @var       REC1
   *            ...
   * @var       REC2
   */
   enum class InputCatType {DATA, RAND, DATA1, RAND1, DATA2, RAND2, REC, REC1, REC2, NONE};

  // box space organization infos
  typedef struct {
    le3gc::dVector posMin;
    le3gc::dVector posMax;

    le3gc::dVector sizeCell;
    le3gc::dVector rSizeCell;

    le3gc::iVector nCell;

    double defaultBoxCell;
    double maxScale;
    double nobjects;
    bool set_from_params;

  } BoxType;


  // test scales range
  typedef struct {
    double min1Dsq;
    double max1Dsq;
    double min1D;
    double max1D;
    double min2Dsq;
    double max2Dsq;
    double min2D;
    double max2D;

    double dim1_binInv;
    double dim2_binInv;

  } ScalesType;

  /**
   * @class     TwoPointCorrelation
   * @brief     Interface Base class to handle the two-point correlation function
   * @details   The two-point correlation function is computed from two catalogues,
   *            the real (data) catalogue and the random catalogue, using the
   *            Landy & Szalay estimator.
   * @version   5.0
   */
  class TwoPointCorrelation {

    friend class ProductsIO; 


  public:

    /**
     * @brief   Default empty constructor
     * @return  Empty TwoPointCorrelation object
     */
    TwoPointCorrelation();


    /**
     * @brief   Constructor
     * @detail  The "param" object is checked for errors in the configuration
     * @param   param <Parameters> object
     * @return  TwoPointCorrelation object with configuration from parameters
     */
    TwoPointCorrelation(const le3gc::Parameters& param);

    /**
     * @brief   Set up 2PCF
     * @detail  Sets minimum and maximum scales et optimal linked-list grid size
     * @return  None
     */
    virtual void SetupPairCount() = 0;

    /**
     * @brief   Set up 2PCF for specific correlation estimator
     * @param   Nr, <int> number of objects
     * @return  None
     */
    void SetupPairCount(int nr)
    {
      m_box.nobjects = nr;
      SetupPairCount();
    }

    /**
     * @brief  Total pair count for normalization
     * @param  cat1 <Catalogue>  catalogue 1
     * @param  cat2 <Catalogue>  catalogue 2
     * @param  pairMax <double>  maximum pair count
     * @return None
     */
    void updateTotalPairs(le3gc::Catalog &cat1, le3gc::Catalog &cat2,
			  double &pairMax, bool allPairs);

    /**
     * @brief   Create the structure to count the catalog pairs
     * @warn    Catalog is modified (sorted)
     * @param   cat, <Catalogue> object
     * @param   catType, extended catalogue type
     * @return  None
     */
    virtual void Indicize(le3gc::Catalog& cat, InputCatType catType) = 0;


    /**
     * @brief   Count pairs between catalogs
     * @details The implementation depends on the counting method: LINKED_LIST or KDTREE
     * @param   cat1, <Catalog> object
     * @param   cat2, <Catalog> object
     * @param   catType1, <InputCatType> catalog type 1
     * @param   catType2, <InputCatType> catalog type 2
     * @param   pairCounts vector<double>, pair counts vector
     * @param   pairMax <double>, maximum pairs
     * @param   allPairs <bool>, count all pairs or half
     */
    virtual void countPairs(le3gc::Catalog& cat1, le3gc::Catalog& cat2,
                            InputCatType catType1, InputCatType catType2,
			    std::vector<double> &pairCounts,
			    double &pairMax, bool allPairs) = 0;

    /**
     * @brief   Wrapper for auto-correlation
     * @param   cat1, <Catalog> object
     * @param   catType1, <InputCatType> catalog type
     * @param   pairCounts vector<double>, pair counts vector
     * @param   pairMax <double>, maximum pairs
     */
    void countPairs(le3gc::Catalog& cat1, InputCatType catType1, 
		    std::vector<double> &pairCounts, double &pairMax)
    {
      countPairs(cat1, cat1, catType1, catType1, pairCounts, pairMax, false);
    }

    /**
     * @brief   Wrapper for cross-correlation
     * @param   cat1, <Catalog> object
     * @param   cat2, <Catalog> object
     * @param   catType1, <InputCatType> catalog type
     * @param   catType2, <InputCatType> catalog type
     * @param   pairCounts vector<double>, pair counts vector
     * @param   pairMax <double>, maximum pairs
     */
    void countPairs(le3gc::Catalog& cat1, le3gc::Catalog& cat2,
		    InputCatType catType1, InputCatType catType2, 
		    std::vector<double> &pairCounts, double &pairMax)
    {
      countPairs(cat1, cat2, catType1, catType2, pairCounts, pairMax, true);
    }

    /**
     * @brief   Clear hash tables where tree are stored
     * @param   catType, <InputCatType> catalog type
     * @return  None
     */
    virtual void clearDataStruct(InputCatType catType) = 0;

    /**
     * @brief   Compute pair separation and fill separation array
     * @param   gal1, <Galaxy> object
     * @param   gal2, <Galaxy> object
     * @param   pairCounts, <vector<double>> counted pairs
     * @param   useWeight, <double> flag
     * @return  none
     */
    void fillPairCounts(le3gc::Galaxy& gal1,
			le3gc::Galaxy& gal2,
			std::vector<double>& pairCounts,
			double useWeight);

    /**
     * @brief   Read pair counts from file
     * @details The structure of the input file and the pairs vector is inferred
     *          from correlation type
     * @param   fileName, <string> path and name of the file to read
     * @param   pairName, <string> type of pairs, DD, DR, RR, S
     * @param   maxpairs, <double> total pair count, read from file header
     * @return  vector<double>,  pair counts vector
     */
    std::vector<double> readPairs(const std::string& fileName,
				  const std::string& pairType,
				  double &maxPairs);

    /**
     * @brief    Read pair counts from file, if flag false
     * @details  Check file size
     * @param    fileName, <string> path and name of the file to read
     * @param    pairName, <string> type of pairs, DD, DR, RR, etc.
     * @param    vector<double>,  pair counts vector
     * @param    maxPairs, <double> total pair count, read from file header
     * @param    compute, <bool> compute or read from file?
     * @param    refSize, <size_t> expected size
     */
    void readPairs(const std::string& fileName,
		   const std::string& pairType,
		   std::vector<double> &pairs,
		   double &maxPairs,
		   bool compute,				  
		   size_t refSize);

    /**
     * @brief   Read XML product and get the FITS file name
     * @details From dataproduct name defines the 1D or 2D pair file product to
     *          open
     * @param   fileName, <string> path and name of the file to read
     * @return  <string> FITS file name
     */
    std::string getPairsFileNameFromProduct(const std::string& xmlName);


    /**
     * @brief   Write pair counts vector in fits file
     * @details General routine for ax 6 catalogs and 5 pair count types
     *          Output file structure depends on the correlation type:
     *          AUTO:  DD, DR, RR  
     *          CROSS: D1D2, D1R2, R1D2, R1R2
     *          AUTO_REC:  DD, DS, SS, RR
     *          CROSS_REC: D1D2, D1S2, S1D2, S1S2, R1R2
     * @param   fileName, <string> path and name of output file
     * @param   pairs1, vector<double> pairs 1
     * @param   pairs2, vector<double> pairs 2
     * @param   pairs3, vector<double> pairs 3
     * @param   pairs4, vector<double> pairs 4
     * @param   pairs5, vector<double> pairs 5
     * @param   max1, double maximum pair count 1
     * @param   max2, double maximum pair count 2
     * @param   max3, double maximum pair count 3
     * @param   max4, double maximum pair count 4
     * @param   max5, double maximum pair count 5
     * @param   cat1 <Catalogue>  catalogue 1
     * @param   cat2 <Catalogue>  catalogue 2
     * @param   cat3 <Catalogue>  catalogue 3
     * @param   cat4 <Catalogue>  catalogue 4
     * @param   cat5 <Catalogue>  catalogue 5
     * @param   cat6 <Catalogue>  catalogue 6
     * @param   cosmo cosmology 
     * @return  none
     */
    void writePairs(const std::string& fileName,
                    const std::vector<double>& pairs1,
		    const std::vector<double>& pairs2,
		    const std::vector<double>& pairs3,
		    const std::vector<double>& pairs4,
		    const std::vector<double>& pairs5,
		    const double max1,
		    const double max2,
		    const double max3,
		    const double max4,
		    const double max5,
                    const le3gc::Catalog& cat1,
                    const le3gc::Catalog& cat2,
		    const le3gc::Catalog& cat3,
                    const le3gc::Catalog& cat4,
		    const le3gc::Catalog& cat5,
		    const le3gc::Catalog& cat6,
                    const le3gc::Cosmology& cosmo);


    /**
     * @brief   Write auto-correlation pair counts in fits file
     * @details Wrapper for two catalogs and three pair counts
     * @param   fileName, <string> path and name of output file
     * @param   pairs1, vector<double> DD pairs
     * @param   pairs2, vector<double> DR pairs
     * @param   pairs3, vector<double> RR pairs
     * @param   max1, <double> DD max pairs
     * @param   max2, <double> DR max pairs
     * @param   max3, <double> RR max pairs
     * @param   cat1 <Catalogue> Data catalogue
     * @param   cat2 <Catalogue> Random  catalogue
     * @param   cosmo cosmology 
     * @return  none
     */
    void writePairs(const std::string& fileName,
                    const std::vector<double>& pairs1,
		    const std::vector<double>& pairs2,
		    const std::vector<double>& pairs3,
		    const double max1,
		    const double max2,
		    const double max3,
                    const le3gc::Catalog& cat1,
                    const le3gc::Catalog& cat2,
                    const le3gc::Cosmology& cosmo) {
      double dum = 0;
      std::vector<double> dumvec(0);
      //Use cat1 as dummy catalog
      writePairs(fileName, pairs1, pairs2, pairs3, dumvec, dumvec,
		 max1, max2, max3, dum, dum,
		 cat1, cat2, cat1, cat1, cat1, cat1, cosmo);
    }

    /**
     * @brief   Write cross-correlation pair counts in fits file
     * @details Wrapper for 4 catalogs and 4 pair counts
     * @param   fileName, <string> path and name of output file
     * @param   pairs1, vector<double> D1D2 pairs
     * @param   pairs2, vector<double> D1R2 pairs
     * @param   pairs3, vector<double> R1D2 pairs
     * @param   pairs3, vector<double> R1R2 pairs
     * @param   max1, <double> D1D2 max pairs
     * @param   max2, <double> D1R2 max pairs
     * @param   max3, <double> R1D2 max pairs
     * @param   max3, <double> R1R2 max pairs
     * @param   cat1 <Catalogue> Data catalogue 1
     * @param   cat2 <Catalogue> Random  catalogue 1
     * @param   cat3 <Catalogue> Data catalogue 2
     * @param   cat4 <Catalogue> Random  catalogue 2
     * @param   cosmo cosmology 
     * @return  none
     */
    void writePairs(const std::string& fileName,
                    const std::vector<double>& pairs1,
		    const std::vector<double>& pairs2,
		    const std::vector<double>& pairs3,
		    const std::vector<double>& pairs4,
		    const double max1,
		    const double max2,
		    const double max3,
		    const double max4,
                    const le3gc::Catalog& cat1,
                    const le3gc::Catalog& cat2,
		    const le3gc::Catalog& cat3,
		    const le3gc::Catalog& cat4,
                    const le3gc::Cosmology& cosmo) {
      double dum = 0;
      std::vector<double> dumvec(0);
      writePairs(fileName, pairs1, pairs2, pairs3, pairs4, dumvec,
		 max1, max2, max3, max4, dum,
		 cat1, cat2, cat3, cat4, cat1, cat1, cosmo);
    }

    /**
     * @brief   Write auto-correlation pair counts with reconstruction in fits file
     * @details Wrapper for 3 catalofs and 4 pair counts
     * @param   fileName, <string> path and name of output file
     * @param   pairs1, vector<double> DD pairs
     * @param   pairs2, vector<double> DS pairs
     * @param   pairs3, vector<double> SS pairs
     * @param   pairs4, vector<double> RR pairs
     * @param   max1, <double> DD max pairs
     * @param   max2, <double> DS max pairs
     * @param   max3, <double> SS max pairs
     * @param   max4, <double> RR max pairs
     * @param   cat1 <Catalogue> Reconstructed data catalogue
     * @param   cat2 <Catalogue> Reconstructed random catalogue
     * @param   cat3 <Catalogue> Random  catalogue
     * @param   cosmo cosmology 
     * @return  none
     */
    void writePairs(const std::string& fileName,
                    const std::vector<double>& pairs1,
		    const std::vector<double>& pairs2,
		    const std::vector<double>& pairs3,
		    const std::vector<double>& pairs4,
		    const double max1,
		    const double max2,
		    const double max3,
		    const double max4,
                    const le3gc::Catalog& cat1,
                    const le3gc::Catalog& cat2,
		    const le3gc::Catalog& cat3,
                    const le3gc::Cosmology& cosmo) {
      double dum = 0;
      std::vector<double> dumvec(0);
      writePairs(fileName, pairs1, pairs2, pairs3, pairs4, dumvec,
		 max1, max2, max3, max4, dum,
		 cat1, cat2, cat3, cat1, cat1, cat1, cosmo);
    }

    /**
     * @brief   Write up to 6 catalogs info in fits file
     * @param   fileName, <string> path and fits file
     * @param   tableName, <string> fits table
     * @param   cat1 <Catalogue> catalogue 1
     * @param   cat2 <Catalogue> catalogue 2
     * @param   cat3 <Catalogue> catalogue 3
     * @param   cat4 <Catalogue> catalogue 4
     * @param   cat5 <Catalogue> catalogue 5
     * @param   cat6 <Catalogue> catalogue 6
      * @return  none
     */
    void writeCatalogInfo(const std::string& fileName,
			  const std::string& tablename,
			  const Catalog& cat1,
			  const Catalog& cat2,
			  const Catalog& cat3,
			  const Catalog& cat4,
			  const Catalog& cat5,
			  const Catalog& cat6);
      
    /**
     * @brief   Write pair counts vector on plain ASCII file
     * @details Output file structure depends on the correlation type:
     * @param   fileName, <string> path and name of output file
     * @param   pairs1, vector<double> pairs 1
     * @param   pairs2, vector<double> pairs 2
     * @param   pairs3, vector<double> pairs 3
     * @param   pairs4, vector<double> pairs 4
     * @param   pairs5, vector<double> pairs 5
     * @return  none
     */
    void writePairsASCII(const std::string& fileName,
                         const std::vector<double>& pairs1,
			 const std::vector<double>& pairs2,
			 const std::vector<double>& pairs3,
			 const std::vector<double>& pairs4,
			 const std::vector<double>& pairs5);
     
    /**
     * @brief   Compute the two-point correlation function from up to 5 pair counts
     * @details This is the general routine. The interpretation of the input pairs depends 
     *             on the chosen estimation method (auto,cross,reconstruction)
     *     The output vector is organized according to the measured statistics:
     *     - 1D: a dim1 length vector
     *     - 2DCART: a dim1*dim2 vector with elements[i,j] in position (i*dim1 + j),
     *               followed by the projected correlation function
     *     - 2DPOL: a dim1*dim2 vector with elements[i,j] in position (i*dim1 + j),
     *                followed by the multipoles
     *
     * @param   counts1 <vector<double>>, pairs 1
     * @param   counts2 <vector<double>>, pairs 2
     * @param   counts3 <vector<double>>, pairs 3
     * @param   counts4 <vector<double>>, pairs 4
     * @param   counts5 <vector<double>>, pairs 5
     * @param   max1 <double>, total number of pairs 1
     * @param   max2 <double>, total number of pairs 2
     * @param   max3 <double>, total number of pairs 3
     * @param   max4 <double>, total number of pairs 4
     * @param   max5 <double>, total number of pairs 5
     * @return  vector<double>, two point correlation values
     */
    std::vector<double> computeCorrelation(const std::vector<double>& counts1,
					   const std::vector<double>& counts2,
					   const std::vector<double>& counts3,
					   const std::vector<double>& counts4,
					   const std::vector<double>& counts5,
					   const double max1,
					   const double max2,
					   const double max3,
					   const double max4,
					   const double max5);

        /**
     * @brief   Wrapper for 3 pair counts
     * @param   counts1 <vector<double>>, pairs 1
     * @param   counts2 <vector<double>>, pairs 2
     * @param   counts3 <vector<double>>, pairs 3
     * @param   max1 <double>, total number of pairs 1
     * @param   max2 <double>, total number of pairs 2
     * @param   max3 <double>, total number of pairs 3
     * @return  vector<double>, two point correlation values
     */
    std::vector<double> computeCorrelation(const std::vector<double>& counts1,
					   const std::vector<double>& counts2,
					   const std::vector<double>& counts3,
					   const double max1,
					   const double max2,
					   const double max3) {
      double dum = 0;
      std::vector<double> dumvec(0);
      return computeCorrelation(counts1, counts2, counts3, dumvec, dumvec,
				max1, max2, max3, dum, dum);
    }
      
        /**
     * @brief   Wrapper for 4 pair counts
     * @param   counts1 <vector<double>>, pairs 1
     * @param   counts2 <vector<double>>, pairs 2
     * @param   counts3 <vector<double>>, pairs 3
     * @param   counts4 <vector<double>>, pairs 4
     * @param   max1 <double>, total number of pairs 1
     * @param   max2 <double>, total number of pairs 2
     * @param   max3 <double>, total number of pairs 3
     * @param   max4 <double>, total number of pairs 4
     * @return  vector<double>, two point correlation values
     */
    std::vector<double> computeCorrelation(const std::vector<double>& counts1,
					   const std::vector<double>& counts2,
					   const std::vector<double>& counts3,
					   const std::vector<double>& counts4,
					   const double max1,
					   const double max2,
					   const double max3,
					   const double max4) {
      double dum = 0;
      std::vector<double> dumvec(0);
      return computeCorrelation(counts1, counts2, counts3, counts4, dumvec,
				max1, max2, max3, max4, dum);
    }

    
    /**
     * @brief   Write the two point correlation function vector in fits file
     * @details The output file structure depends on the correlation function type
     *          Catalog info is written into the fits header
     * @param   fileName <string>, path and name of output file
     * @param   fileNameInt <string>, path and name of output file for projected correlation function and multipoles
     * @param   correlation <vector<double>>, the ordered correlation function values
     * @param   cat1 <Catalogue>  catalogue 1
     * @param   cat2 <Catalogue>  catalogue 2
     * @param   cat3 <Catalogue>  catalogue 3
     * @param   cat4 <Catalogue>  catalogue 4
     * @param   cat5 <Catalogue>  catalogue 5
     * @param   cat6 <Catalogue>  catalogue 6
     * @param   cosmo cosmology 
     * @return  none
     */
    void writeCorrelation(const std::string& fileName,
			  const std::string& fileNameInt,
                          const std::vector<double>& correlation,
                          const le3gc::Catalog& cat1,
                          const le3gc::Catalog& cat2,
			  const le3gc::Catalog& cat3,
                          const le3gc::Catalog& cat4,
			  const le3gc::Catalog& cat5,
			  const le3gc::Catalog& cat6,
                          const le3gc::Cosmology& cosmo);

        /**
     * @brief   Wrapper for 2 catalogs
     * @param   fileName <string>, path and name of output file
     * @param   fileNameInt <string>, path and name of output file for projected correlation function and multipoles
     * @param   correlation <vector<double>>, the ordered correlation function values
     * @param   cat1 <Catalogue>  catalogue 1
     * @param   cat2 <Catalogue>  catalogue 2
     * @param   cosmo cosmology 
     * @return  none
     */
    void writeCorrelation(const std::string& fileName,
			  const std::string& fileNameInt,
                          const std::vector<double>& correlation,
                          const le3gc::Catalog& cat1,
                          const le3gc::Catalog& cat2,
                          const le3gc::Cosmology& cosmo) {
      //Use cat1 as dummy catalog
       writeCorrelation(fileName,fileNameInt,correlation,cat1,cat2,cat1,cat1,cat1,cat1,cosmo);
    }
    

        /**
     * @brief   Wrapper for 3 catalogs
     * @param   fileName <string>, path and name of output file
     * @param   fileNameInt <string>, path and name of output file for projected correlation function and multipoles
     * @param   correlation <vector<double>>, the ordered correlation function values
     * @param   cat1 <Catalogue>  catalogue 1
     * @param   cat2 <Catalogue>  catalogue 2
     * @param   cat3 <Catalogue>  catalogue 3
     * @param   cosmo cosmology 
     * @return  none
     */
    void writeCorrelation(const std::string& fileName,
			  const std::string& fileNameInt,
                          const std::vector<double>& correlation,
                          const le3gc::Catalog& cat1,
                          const le3gc::Catalog& cat2,
			  const le3gc::Catalog& cat3,
                          const le3gc::Cosmology& cosmo) {
      writeCorrelation(fileName,fileNameInt,correlation,cat1,cat2,cat3,cat1,cat1,cat1,cosmo);
    }
    
        /**
     * @brief   Wrapper for 4 catalogs
     * @param   fileName <string>, path and name of output file
     * @param   fileNameInt <string>, path and name of output file for projected correlation function and multipoles
     * @param   correlation <vector<double>>, the ordered correlation function values
     * @param   cat1 <Catalogue>  catalogue 1
     * @param   cat2 <Catalogue>  catalogue 2
     * @param   cat3 <Catalogue>  catalogue 3
     * @param   cat4 <Catalogue>  catalogue 4
     * @param   cosmo cosmology 
     * @return  none
     */
    void writeCorrelation(const std::string& fileName,
			  const std::string& fileNameInt,
                          const std::vector<double>& correlation,
                          const le3gc::Catalog& cat1,
                          const le3gc::Catalog& cat2,
			  const le3gc::Catalog& cat3,
                          const le3gc::Catalog& cat4,
                          const le3gc::Cosmology& cosmo) {
      writeCorrelation(fileName,fileNameInt,correlation,cat1,cat2,cat3,cat4,cat1,cat1,cosmo);
    }  


    /**
     * @brief   Write the two point correlation function vector on ASCII file
     * @details The output file structure depends on the correlation function type
     * @param   correlation <vector<double>>, the ordered correlation function values
     * @param   fileName <string>, path and name of output file 
     * @return  none
     */
    void writeCorrelationASCII(const std::vector<double>& correlation,
                               const std::string& fileName,
			       const std::string& fileNameInt);


    /**
     * @brief   Write the two point correlation product
     * @details The output file structure depends on the correlation function type
     * @param   fitsName, <string> path and name of correlation fits file
     * @param   prodName, <string> path and name of product file
     * @return  none
     */
    void writeCorrelationProduct(const std::string& fitsName,
                                 const std::string& prodName);


    /**
     * @brief   Write table catalog information to fits file header
     * @details TBD
     * @param   fileName, <string> path and name of fits file
     * @param   tableName, <string> path and name of table
     * @param   typeName, <string> catalog type
     * @param   cat, <Catalog> catalog
     * @return  none
     */
    void writeCatInfoToFITS(const std::string& fileName, 
			    const std::string& tableName,
			    const std::string& typeName,
			    const Catalog& cat);

    /**
     * @brief   Write table information to fits file header
     * @details TBD
     * @param   fileName, <string> path and name of fits file
     * @param   tableName, <string> path and name of table
     * @return  none
     */
    void writeInfoToFITS(const std::string& fileName,
                         const std::string& tableName,
                         std::string& statistics);

    /**
     * @brief   Write max pairs information to fits file header
     * @details Interpretation of the 5 inputs depends on chosen estimation method
     * @param   fileName, <string> path and name of fits file
     * @param   tableName, <string> path and name of table
     * @param   max1, <double> max pairs 1
     * @param   max2, <double> max pairs 2
     * @param   max3, <double> max pairs 3
     * @param   max4, <double> max pairs 4
     * @param   max5, <double> max pairs 5
     * @return  none
     */
    void writePairsInfoToFITS(const std::string& fileName,
			      const std::string& tableName,
			      const double max1,
			      const double max2,
			      const double max3,
			      const double max4,
			      const double max5);

    /**
     * @brief   Compute the axis values for the two point correlation function plot
     * @param   ax <AxisType> [DIM1,DIM2], where to compute the scale
     * @return  vector<double> two point correlation function axis scale values
     */
    std::vector<double> getAxisScale(const AxisType& ax);


    /**
     * @brief   Initialize box
     * @param   box_in_min, box minimum
     * @param   box_in_max, box maximum
     * @return  none
     */
    void initBox(dVector box_in_min,
                 dVector box_in_max);

    /**
     * @brief   Enlarge the enclosing box m_box
     * @param   box, <BoxType> external box
     * @return  none
     */
    void updateBox(BoxType Box);

   /**
     * @brief   Whether m_box is already set from input parameters
     * @return  Flag
     */
    bool boxSet() {return m_box.set_from_params;}

    /**
     * @brief   Get the enclosing box from given vectors
     * @param   box_min, <dVector> inner corner
     * @param   box_max, <dVector> outer corner
     * @param   nobjects <int> number of objects in the original catalogue
     * @return  Box
     */
    BoxType boxFromVector(dVector box_min,
                          dVector box_max,
                          int nobjects);

    /**
     * @brief   Get catalog size and update the enclosing box
     * @param   cat <Catalog>, catalog
     * @param   cattype <string>, catalog type (R,D etc.) for log
     * @return  Number of objects
     */
    int updateBoundingBox(le3gc::Catalog& cat, std::string cattype);

     /**
     * @brief   Set up 2PCF parameters
     * @param   params, 2PCF parameters
     * @return  pointer to TwoPointCorrelation object
     */
    static std::unique_ptr<TwoPointCorrelation> make2PCF(const le3gc::Parameters& params);

    /**
     * @brief   Set optimal split factor
     * @param   ndata <int>, catalog size
     * @param   ndata2 <int>, catalog size
     * @param   nRandom <int>, catalog size
     * @param   nRandom2 <int>, catalog size
     * @return  split factor
     */
    int setSplit(int nData, int ndata2, int nRandom, int nRandom2);
       
    /**
     * @brief   Return true is statistics is 1D
     * @param   None
     * @return  bool, true if 1D statistics
     */
    bool statIs1D() {
      return  (CorrelationType::AUTO_1D == m_statistics ||
	       CorrelationType::CROSS_1D == m_statistics ||
	       CorrelationType::AUTO_REC_1D == m_statistics ||
	       CorrelationType::CROSS_REC_1D == m_statistics);
    }

    /**
     * @brief   Return true is statistics is 2D
     * @param   None
     * @return  bool, true if 2D statistics
     */
    bool statIs2D() {
      return  (CorrelationType::AUTO_2DPOL == m_statistics ||
	       CorrelationType::CROSS_2DPOL == m_statistics ||
               CorrelationType::AUTO_2DCART == m_statistics ||
	       CorrelationType::CROSS_2DCART == m_statistics ||
	       CorrelationType::AUTO_REC_2DPOL == m_statistics ||
	       CorrelationType::CROSS_REC_2DPOL == m_statistics ||
               CorrelationType::AUTO_REC_2DCART == m_statistics ||
	       CorrelationType::CROSS_REC_2DCART == m_statistics);
    }

    /**
     * @brief   Return true is statistics is AUTO 
     * @param   None
     * @return  bool, true if AUTO statistics
     */
    bool statIsAuto() {
      return  (CorrelationType::AUTO_1D == m_statistics ||
	       CorrelationType::AUTO_2DPOL == m_statistics || 
	       CorrelationType::AUTO_2DCART == m_statistics ||
	       CorrelationType::AUTO_REC_1D == m_statistics ||
	       CorrelationType::AUTO_REC_2DPOL == m_statistics || 
	       CorrelationType::AUTO_REC_2DCART == m_statistics);
    }

    /**
     * @brief   Return true is statistics is CROSS 
     * @param   None
     * @return  bool, true if CROSS statistics
     */
    bool statIsCross() {
      return  (CorrelationType::CROSS_1D == m_statistics ||
	       CorrelationType::CROSS_2DPOL == m_statistics || 
	       CorrelationType::CROSS_2DCART == m_statistics ||
	       CorrelationType::CROSS_REC_1D == m_statistics ||
	       CorrelationType::CROSS_REC_2DPOL == m_statistics || 
	       CorrelationType::CROSS_REC_2DCART == m_statistics);
    }

    /**
     * @brief   Return true is statistics involves reconstruction
     * @param   None
     * @return  bool, true if reconstructed catalog used
     */
    bool statIsRec() {
      return  (CorrelationType::AUTO_REC_1D == m_statistics ||
	       CorrelationType::CROSS_REC_1D == m_statistics ||
	       CorrelationType::AUTO_REC_2DCART == m_statistics ||
	       CorrelationType::CROSS_REC_2DCART == m_statistics ||
	       CorrelationType::AUTO_REC_2DPOL == m_statistics ||
	       CorrelationType::CROSS_REC_2DPOL == m_statistics);
    }

    bool statIsAutoRec() {
      return statIsAuto() && statIsRec();
    }

    bool statIsAutoNoRec() {
      return statIsAuto() && !statIsRec();
    }

    bool statIsCrossRec() {
      return statIsCross() && statIsRec();
    }

    bool statIsCrossNoRec() {
      return statIsCross() && !statIsRec();
    }

    /**
     * @brief   Return true is statistics is 2DPOL
     * @param   None
     * @return  bool, true if 2DPOL statistics
     */
    bool statIs2DPOL() {
      return  (CorrelationType::AUTO_2DPOL == m_statistics ||
	       CorrelationType::CROSS_2DPOL == m_statistics ||
	       CorrelationType::AUTO_REC_2DPOL == m_statistics ||
	       CorrelationType::CROSS_REC_2DPOL == m_statistics);
    }

    /**
     * @brief   Return true is statistics is 2DCART
     * @param   None
     * @return  bool, true if 2DCART statistics
     */
    bool statIs2DCART() {
      return  (CorrelationType::AUTO_2DCART == m_statistics ||
	       CorrelationType::CROSS_2DCART == m_statistics ||
	       CorrelationType::AUTO_REC_2DCART == m_statistics ||
	       CorrelationType::CROSS_REC_2DCART == m_statistics);
    }

    /**
     * @brief Destructor
     */
     virtual ~TwoPointCorrelation() = default;


     /**
     * @brief    Get output 2pcf base name
     * @details  Generate output filename according to the Euclid standard
     * @param    None
     * @return   std::string containing the output base name
     */
    std::string getFitsName(std::string& statistics);

     /**
     * @brief    Get output 2pcf base name
     * @details  Generate output filename according to the Euclid standard
     * @param    None
     * @return   std::string containing the output base name
     */
    std::string getPairsFitsName(std::string& statistics);


  protected:

    /// Structure with all the min/max scales used in the computations
    ScalesType m_scales;

    /// structure with all the informations about the catalog enclosing volume
    BoxType m_box;

    /// binning type for first dimension [LINEAR, LOG]
    BinningType m_scale1D;

    /// binning type for second dimension [LINEAR, LOG]
    BinningType m_scale2D;

    // correlation function type [AUTO_1D, AUTO_2DCart, AUTO_2DPol, NONE]
    CorrelationType m_statistics;

    /// number of bins in the first dimension
    int m_nBin1D;

    /// minimum pair separation in the first dimension
    double m_min1D;

    /// maximum pair separation in the first dimension
    double m_max1D;

    /// number of bins in the second dimension
    int m_nBin2D;

    /// minimum pair separation in the second dimension
    double m_min2D;

    /// maximum pair separation in the second dimension
    double m_max2D;

    /// maximum separation along the line of sight, used for the projected correlation function
    double m_piMax;

    /// enable objects weight when computing correlation
    bool m_useWeight;

    // enable/disable legacy TXT file with results
    bool m_useTXT;

    // counting method used [LINKED_LIST, KD_TREE]
    std::string m_method;

    // split factor
    int m_nsplit;

    // mu->-mu mirroring in 2DPOL
    bool m_mirroring;

    // line of sight definition in 2DPOL [mid-point: 0, bissector: 1, end-point: 2]
    int m_losDef;

    // release
    std::string m_release;

    /// internal map between statistics type and name
    static boost::bimap<std::string, CorrelationType> m_validStatistics;
    static boost::bimap<std::string, le3gc::BinningType> m_validScales;

    /**
     * @brief   Read 2PCF parameters from input parfile object
     * @param   params <LE3CommonLibs::Parameters> object
     * @return  None
     */
    void m_getParameters(const le3gc::Parameters& params);

    /**
     * @brief   Check requested correlation function type
     *
     * @param name <string>, name of the correlation function
     * [AUTO_1D, AUTO_2DCart, AUTO_2DPol, CROSS_1D, CROSS_2DCart, CROSS_2DPol, NONE]
     *
     * @return  CorrelationType
     */
    CorrelationType m_getStatistics(const std::string& statName);

    /**
     * @brief   Check requested binning type
     *
     * @param name <string>, name of the correlation binning type
     * [LINEAR, LOG]
     *
     * @return  <BinningType>
     */
    le3gc::BinningType m_getScaleType(const std::string& name);

    /**
     * @brief   Compute the scales needed to count pairs
     * @param   None
     * @return  None
     */
    ScalesType m_getScales();

    /**
     * @brief   Get the catalog enclosing box dimensions
     * @param   cat, <Catalog> object
     * @return  <BoxType> object with cells number, dimensions
     */
    BoxType m_getBoxSize(le3gc::Catalog& cat);

  }; /* End of TwoPointCorrelation class */
} /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */

#endif
