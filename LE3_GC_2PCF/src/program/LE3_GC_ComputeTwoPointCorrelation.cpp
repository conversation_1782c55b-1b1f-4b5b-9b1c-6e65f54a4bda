/*
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      src/program/LE3_GC_ComputeTwoPointCorrelation.cpp
 * @date      01/01/18
 * <AUTHOR> <federic<PERSON>.<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#include <map>
#include <string>
#include <vector>
#include <chrono>
#include <fstream>

#include <boost/program_options.hpp>
#include "ElementsKernel/ProgramHeaders.h"

#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"
#include "LE3_GC_Libraries/Cosmology.h"
#include "LE3_GC_Libraries/Utils.h"
#include "LE3_GC_2PCF/TwoPointCorrelation.h"

namespace po = boost::program_options;
namespace fs = boost::filesystem;

using std::string;
using std::vector;
using std::unique_ptr;
using std::chrono::duration;
using std::chrono::high_resolution_clock;

using le3gc::dVector;
using le3gc::Parameters;
using le3gc::Catalog;
using le3gc::Cosmology;
using le3gc::LE3_GC_2PCF::BoxType;
using le3gc::LE3_GC_2PCF::TwoPointCorrelation;

using le3gc::getDateTimeString;


class LE3_GC_ComputeTwoPointCorrelation : public Elements::Program {

public:

  po::options_description defineSpecificProgramOptions() override {
    po::options_description options {};

    // pipeline mode: EUCLID or STANDALONE
    options.add_options()

      // base parameter file
      ("parfile", po::value<string>()->required(), "input parameter file")

      // input workingdirectory: default is user_area
      ("workdir", po::value<string>()->default_value(getenv("LE3_GC_TWOPOINTCORRELATION_PROJECT_ROOT")), \
       "base working folder")

      // input log folder where to save logs (EUCLID)
      ("logdir", po::value<string>()->default_value(""), "logs folder")

      // input data catalog product (EUCLID)
      ("input_sky", po::value<string>()->default_value(""), "Galaxy catalog FITS product name")

      // input random catalog product (EUCLID)
      ("input_ref", po::value<string>()->default_value(""), "Random catalog FITS product name")

      // input reconstructed random catalog product (EUCLID)
      ("input_rec", po::value<string>()->default_value(""), "Reconstructed catalog FITS product name")

       // input data catalog product (EUCLID)
      ("input_sky2", po::value<string>()->default_value(""), "Secondary galaxy catalog FITS product name")

      // input random catalog product (EUCLID)
      ("input_ref2", po::value<string>()->default_value(""), "Secondary random catalog FITS product name")

      // input reconstructed random catalog product (EUCLID)
      ("input_rec2", po::value<string>()->default_value(""), "Reconstructed catalog FITS product name")

      // input pair product (EUCLID)
      ("input_pair", po::value<string>()->default_value(""), "Input pair FITS product")

      // output LE3-GC-2PCF pair + Correlation file Euclid Product
      ("euclid_results", po::value<string>()->default_value("out.json"), "Output intermediate JSON product");

    return options;
  }


  // ----- MAIN ----- //
  Elements::ExitCode mainMethod(std::map<std::string, po::variable_value>& args) override {

    // Start time and int logger for PK main
    auto ts = std::chrono::high_resolution_clock::now();
    Elements::Logging logger = Elements::Logging::getLogger("LE3-GC---2PCF--");

    logger.info("# Starting LE3_CG_ComputeTwoPointCorrelation");
    logger.info("# ------------------------------------------");
    logger.info() << "Using " << omp_get_max_threads() << " thread(s) max.";

    // ----- Configure Working Directory ----- //
    fs::path workdir = fs::path(args["workdir"].as<std::string>());

    // create "data" dir
    fs::create_directory(workdir / fs::path("data"));


    // ----- Configure Parameters ----- //
    fs::path inputParFile = workdir / fs::path(args["parfile"].as<std::string>());
    if (!(fs::is_regular_file(inputParFile))) {
      throw std::runtime_error("Parameter file not found:" + inputParFile.native());
    }
    // Read parameters from configuration file
    le3gc::Parameters params(inputParFile.native());

    // ---- initialize 2PCF ---- //
    std::unique_ptr<TwoPointCorrelation> twoPoint = TwoPointCorrelation::make2PCF(params);

    // ----- Configure Cosmology ----- //
    le3gc::Cosmology cosmology(params);

    // ----- Configure Data Catalog ----- //
    // Check that the input sky FITS file exists
    std::string nameCatData = (workdir / fs::path(params.find<std::string>("Catalog.Galaxy/filename"))).string();
    std::string nameFromCommandLine = args["input_sky"].as<std::string>();
    if (!nameFromCommandLine.empty()) {
        nameCatData = (workdir / fs::path(nameFromCommandLine)).string();
    }
    if (!(fs::is_regular_file(nameCatData))) {
      throw std::runtime_error("Galaxy Catalog file not found: " + nameCatData);
    }
    // ----- Configure Random Catalog ----- //
    // Check that the input rand FITS file exists
    std::string nameCatRand = (workdir / fs::path(params.find<std::string>("Catalog.Random/filename"))).string();
    nameFromCommandLine = args["input_ref"].as<std::string>();
    if (!nameFromCommandLine.empty()) {
      nameCatRand = (workdir / fs::path(nameFromCommandLine)).string();
    }
    if (!(fs::is_regular_file(nameCatRand))) {
      throw std::runtime_error("Random Catalog file not found: " + nameCatRand);
    }

    std::string nameCatRec;
    if (twoPoint->statIsRec()) {
      nameCatRec = (workdir / fs::path(args["input_rec"].as<std::string>())).string();

      if (!(fs::is_regular_file(nameCatRec))) {
        throw std::runtime_error("Recombined Catalog file not found: " + nameCatRec);
      }
    }

    std::string nameCatData2;
    std::string nameCatRand2;
    std::string nameCatRec2;
    if (twoPoint->statIsCross()) {
      nameCatData2 = (workdir / fs::path(args["input_sky2"].as<std::string>())).string();
      if (!(fs::is_regular_file(nameCatData2))) {
        throw std::runtime_error("Galaxy Catalog file n.2 not found: " + nameCatData2);
      }
      nameCatRand2 = (workdir / fs::path(args["input_ref2"].as<std::string>())).string();
      if (!(fs::is_regular_file(nameCatRand2))) {
        throw std::runtime_error("Random Catalog file n.2 not found: " + nameCatRand2);
      }
      if (twoPoint->statIsRec()) {
        nameCatRec2 = (workdir / fs::path(args["input_rec2"].as<std::string>())).string();
        if (!(fs::is_regular_file(nameCatRec2))) {
          throw std::runtime_error("Recombined Catalog file not found: " + nameCatRec2);
        }
      }
    }

    // ----- Instantiate, configure, open catalogs (FITS or ASCII) ----- //

    // primary data and random catalogs
    unique_ptr<Catalog> dataCat;
    unique_ptr<Catalog> randCat;
    unique_ptr<Catalog> recCat;

    // data catalog: open for reading
    logger.info() << "Opening D1 catalog " << nameCatData;
    dataCat = Catalog::makeDataCatalog(nameCatData);
    dataCat->configure(params, cosmology);
    dataCat->openAndParse(nameCatData);

    // random catalog: get size and bounding box
    logger.info() << "Opening R1 catalog " << nameCatRand;
    randCat = Catalog::makeRandCatalog(nameCatRand);
    randCat->configure(params, cosmology);
    randCat->openAndParse(nameCatRand);

    if (twoPoint->statIsRec()) {
      logger.info() << "Opening S1 catalog " << nameCatRec;
      recCat = Catalog::makeRandCatalog(nameCatRec);
      recCat->configure(params, cosmology);
      recCat->openAndParse(nameCatRec);
    }

    // secondary data and random catalogs for cross-correlation
    unique_ptr<Catalog> dataCat2;
    unique_ptr<Catalog> randCat2;
    unique_ptr<Catalog> recCat2;

    if (twoPoint->statIsCross()) {
      // data catalog
      logger.info() << "Opening D2 catalog " << nameCatData2;
      dataCat2 = Catalog::makeDataCatalog(nameCatData2);
      dataCat2->configure(params, cosmology);
      dataCat2->openAndParse(nameCatData2);

      // random sub-catalog, used in the loop of the split method
      logger.info() << "Opening R2 catalog " << nameCatRand2;
      randCat2 = Catalog::makeRandCatalog(nameCatRand2);
      randCat2->configure(params, cosmology);
      randCat2->openAndParse(nameCatRand2);

      if (twoPoint->statIsRec()) {
        logger.info() << "Opening S2 catalog " << nameCatRec2;
        recCat2 = Catalog::makeRandCatalog(nameCatRec2);
        recCat2->configure(params, cosmology);
        recCat2->openAndParse(nameCatRec2);
      }
    }

    // get pairs calculation conditions
    bool computeDD = true, computeDR = true, computeRR = true;
    bool computeDS = true, computeSS = true;
    bool computeD1D2 = true, computeD1R2 = true, computeR1D2 = true, computeR1R2 = true;
    bool computeD1S2 = true, computeS1D2 = true, computeS1S2 = true;

    if (twoPoint->statIsAuto()) {
      computeDD = params.find<bool>("2PCF/compute_DD", "true");
      computeRR = params.find<bool>("2PCF/compute_RR", "true");
      if (twoPoint->statIsRec()) {
        computeDS = params.find<bool>("2PCF/compute_DS", "true");
        computeSS = params.find<bool>("2PCF/compute_SS", "true");
      }
      else {
        computeDR = params.find<bool>("2PCF/compute_DR", "true");
      }
    }
    if (twoPoint->statIsCross()) {
      computeD1D2 = params.find<bool>("2PCF/compute_D1D2", "true");
      computeR1R2 = params.find<bool>("2PCF/compute_R1R2", "true");
      if (twoPoint->statIsRec()) {
        computeD1S2 = params.find<bool>("2PCF/compute_D1S2", "true");
        computeS1D2 = params.find<bool>("2PCF/compute_S1D2", "true");
        computeS1S2 = params.find<bool>("2PCF/compute_S1S2", "true");
      }
      else {
        computeD1R2 = params.find<bool>("2PCF/compute_D1R2", "true");
        computeR1D2 = params.find<bool>("2PCF/compute_R1D2", "true");
      }
    }

    // get input pairs file for current 2PCF configuration if pair computing disabled
    std::string inFileNamePairs ("");
    if (!computeDD || !computeRR || !computeDR || !computeDS || !computeSS ||
        !computeD1D2 || !computeD1R2 || !computeR1D2  || !computeR1R2 ||
  !computeD1S2 || !computeS1D2 || !computeS1S2) {
      inFileNamePairs = (workdir / fs::path(args["input_pair"].as<std::string>())).string();
    }

    // initialize catalog object numbers
    int nData = 0, nRandom = 0, nRec = 0;
    int nData2 = 0, nRandom2 = 0, nRec2 = 0;

    const le3gc::LE3_GC_2PCF::InputCatType DCAT1 = le3gc::LE3_GC_2PCF::InputCatType::DATA1;
    const le3gc::LE3_GC_2PCF::InputCatType DCAT2 = le3gc::LE3_GC_2PCF::InputCatType::DATA2;
    const le3gc::LE3_GC_2PCF::InputCatType RCAT1 = le3gc::LE3_GC_2PCF::InputCatType::RAND1;
    const le3gc::LE3_GC_2PCF::InputCatType RCAT2 = le3gc::LE3_GC_2PCF::InputCatType::RAND2;
    const le3gc::LE3_GC_2PCF::InputCatType SCAT1 = le3gc::LE3_GC_2PCF::InputCatType::REC1;
    const le3gc::LE3_GC_2PCF::InputCatType SCAT2 = le3gc::LE3_GC_2PCF::InputCatType::REC2;

// ---- Read data catalogue to get size and to set box

    nData = twoPoint->updateBoundingBox(*dataCat,twoPoint->statIsAuto() ? "D" : "D1");
    nRandom = twoPoint->updateBoundingBox(*randCat,twoPoint->statIsAuto() ? "R" : "R1");
    if (twoPoint->statIsRec()) nRec = twoPoint->updateBoundingBox(*recCat,twoPoint->statIsAuto() ? "S" : "S1");

    if (twoPoint->statIsCross()) {
      nData2 = twoPoint->updateBoundingBox(*dataCat2, "D2");
      nRandom2 = twoPoint->updateBoundingBox(*randCat2, "R2");
      if (twoPoint->statIsRec()) {
        nRec2 = twoPoint->updateBoundingBox(*recCat2, "S2");
      }
    }

    // ---- set up pair counting structure
    twoPoint->SetupPairCount(static_cast<int>(std::max(nRandom, nData)));

    // max pairs numbers
    double maxDD = 0.0, maxDR = 0.0, maxRR = 0.0;
    double maxDS = 0.0, maxSS = 0.0;
    double maxD1D2 = 0.0, maxD1R2 = 0.0, maxR1D2 = 0.0, maxR1R2 = 0.0;
    double maxD1S2 = 0.0, maxS1D2 = 0.0, maxS1S2 = 0.0;

    // pair vectors
    vector<double> pairsDD(0), pairsDR(0), pairsRR(0);
    vector<double> pairsDS(0), pairsSS(0);
    vector<double> pairsD1D2(0), pairsD1R2(0), pairsR1D2(0), pairsR1R2(0);
    vector<double> pairsD1S2(0), pairsS1D2(0), pairsS1S2(0);

    // ---- Indicize the data catalog

    dataCat->loadFromFile();
    dataCat->coordinatesToCartesian(cosmology);
    twoPoint->Indicize(*dataCat, DCAT1);

    if (twoPoint->statIsCross()) {
      dataCat2->loadFromFile();
      dataCat2->coordinatesToCartesian(cosmology);
      twoPoint->Indicize(*dataCat2, DCAT2);
    }

    // Count data pairs
    if (true == computeDD && twoPoint->statIsAuto()) {
      logger.info() << "DD pairs: ";
      twoPoint->countPairs(*dataCat, DCAT1, pairsDD, maxDD);
    }
    if (true == computeD1D2 && twoPoint->statIsCross()) {
      logger.info() << "D1D2 pairs: ";
      twoPoint->countPairs(*dataCat, *dataCat2, DCAT1, DCAT2, pairsD1D2, maxD1D2);
    }

    // ---- Read precomputed pairs from file -----

    if (twoPoint->statIsAuto()) {
      twoPoint->readPairs(inFileNamePairs, "DD", pairsDD, maxDD, computeDD, 0);
      twoPoint->readPairs(inFileNamePairs, "DR", pairsDR, maxDR, computeDR, pairsDD.size());
      twoPoint->readPairs(inFileNamePairs, "RR", pairsRR, maxRR, computeRR, pairsDD.size());
      twoPoint->readPairs(inFileNamePairs, "DS", pairsDS, maxDS, computeDS, pairsDD.size());
      twoPoint->readPairs(inFileNamePairs, "SS", pairsSS, maxSS, computeSS, pairsDD.size());
    }
    if (twoPoint->statIsCross()) {
      twoPoint->readPairs(inFileNamePairs, "D1D2", pairsD1D2, maxD1D2, computeD1D2, 0);
      twoPoint->readPairs(inFileNamePairs, "D1R2", pairsD1R2, maxD1R2, computeD1R2, pairsD1D2.size());
      twoPoint->readPairs(inFileNamePairs, "R1D2", pairsR1D2, maxR1D2, computeR1D2, pairsD1D2.size());
      twoPoint->readPairs(inFileNamePairs, "R1R2", pairsR1R2, maxR1R2, computeR1R2, pairsD1D2.size());
      twoPoint->readPairs(inFileNamePairs, "D1S2", pairsD1S2, maxD1S2, computeD1S2, pairsD1D2.size());
      twoPoint->readPairs(inFileNamePairs, "S1D2", pairsS1D2, maxS1D2, computeS1D2, pairsD1D2.size());
      twoPoint->readPairs(inFileNamePairs, "S1S2", pairsS1S2, maxS1S2, computeS1S2, pairsD1D2.size());
    }

    //Find the split factor
    int splitFactor = params.find<int>("2PCF/split_factor",0);
    logger.info() << "split_factor = " <<splitFactor;

    if (splitFactor == 0) {
      splitFactor = twoPoint->setSplit(nData,nData2,nRandom,nRandom2);
      logger.info() << "Automatic split mode";
      logger.info() << "Setting split factor to " <<splitFactor;
    }

    // Number of objects per split
    int nRandLines = 0;
    int nRecLines = 0;
    int nRandLines2 = 0;
    int nRecLines2 = 0;

    nRandLines = nRandom/splitFactor;
    if (nRandLines*splitFactor < nRandom) nRandLines++;

    if (twoPoint->statIsRec()) {
      nRecLines = nRec/splitFactor;
      if (nRecLines*splitFactor < nRec) {
        nRecLines++;
       }
    }

    if (twoPoint->statIsCross()) {
      nRandLines2 = nRandom2/splitFactor;
      if (nRandLines2*splitFactor < nRandom2) {
        nRandLines2++;
      }

      if (twoPoint->statIsRec()) {
        nRecLines2 = nRec2/splitFactor;
        if (nRecLines2*splitFactor < nRec2) {
          nRecLines2++;
        }
      }
    }

    // loop over random catalog splits
    int nRandDone = 0;
    int nRandDone2 = 0;
    int nRecDone = 0;
    int nRecDone2 = 0;

    for (int iloop = 0; iloop<splitFactor; iloop++) {
      logger.info() << "Split " << iloop + 1 << " / " << splitFactor;

      // Read random and reconstructed catalogs
      logger.info() << "Reading R1 catalog";
      nRandLines = std::min(nRandLines, nRandom-nRandDone);
      nRandDone += nRandLines;

      randCat->loadFromFile(nRandLines);
      randCat->coordinatesToCartesian(cosmology);
      twoPoint->Indicize(*randCat, RCAT1);

      if (twoPoint->statIsRec()) {
        logger.info() << "Reading S1 catalog";
        nRecLines = std::min(nRecLines, nRec-nRecDone);
        nRecDone += nRecLines;

        recCat->loadFromFile(nRecLines);
        recCat->coordinatesToCartesian(cosmology);
        twoPoint->Indicize(*recCat, SCAT1);
      }

      if (twoPoint->statIsCross()) {
        logger.info() << "Reading R2 catalog";
        nRandLines2 = std::min(nRandLines2, nRandom2-nRandDone2);
        nRandDone2 += nRandLines2;

        randCat2->loadFromFile(nRandLines2);
        randCat2->coordinatesToCartesian(cosmology);
        twoPoint->Indicize(*randCat2, RCAT2);

        if (twoPoint->statIsRec()) {
          logger.info() << "Reading S2 catalog";
          nRecLines2 = std::min(nRecLines2, nRec2-nRecDone2);
          nRecDone2 += nRecLines2;

          recCat2->loadFromFile(nRecLines2);
          recCat2->coordinatesToCartesian(cosmology);
          twoPoint->Indicize(*recCat2, SCAT2);
        }
      }

      // Count pairs; RR, DR, DS, SS, R1R2, D1S2, S1D2, S1S2
      if (twoPoint->statIsAuto()) {
        if (twoPoint->statIsRec()) {
          if (true == computeDS) {
            logger.info() << "DS pairs: ";
            twoPoint->countPairs(*dataCat, *recCat, DCAT1, SCAT1, pairsDS, maxDS);
          }

          if (true == computeRR) {
            logger.info() << "RR pairs: ";
            twoPoint->countPairs(*randCat, RCAT1, pairsRR, maxRR);
          }

          if (true == computeSS) {
            logger.info() << "SS pairs: ";
            twoPoint->countPairs(*recCat, SCAT1, pairsSS, maxSS);
          }
        } else {
          if (true == computeDR) {
            logger.info() << "DR pairs: ";
            twoPoint->countPairs(*dataCat, *randCat, DCAT1, RCAT1, pairsDR, maxDR);
          }

          if (true == computeRR) {
            logger.info() << "RR pairs: ";
            twoPoint->countPairs(*randCat, RCAT1, pairsRR, maxRR);
          }
        }
      }

      if (twoPoint->statIsCross()) {
        if (twoPoint->statIsRec()) {
          if (true == computeD1S2) {
            logger.info() << "D1S2 pairs: ";
            twoPoint->countPairs(*dataCat, *recCat2, DCAT1, SCAT2, pairsD1S2, maxD1S2);
          }
          if (true == computeS1D2) {
            logger.info() << "S1D2 pairs: ";
            twoPoint->countPairs(*recCat, *dataCat2, SCAT1, DCAT2, pairsS1D2, maxS1D2);
          }

          if (true == computeR1R2) {
            logger.info() << "R1R2 pairs: ";
            twoPoint->countPairs(*randCat, *randCat2, RCAT1, RCAT2, pairsR1R2, maxR1R2);
          }

          if (true == computeS1S2) {
            logger.info() << "S1S2 pairs: ";
            twoPoint->countPairs(*recCat, *recCat2, SCAT1, SCAT2, pairsS1S2, maxS1S2);
          }
        } else {
          if (true == computeD1R2) {
            logger.info() << "D1R2 pairs: ";
            twoPoint->countPairs(*dataCat, *randCat2, DCAT1, RCAT2, pairsD1R2, maxD1R2);
          }

          if (true == computeR1D2) {
            logger.info() << "R1D2 pairs: ";
            twoPoint->countPairs(*randCat, *dataCat2, RCAT1, DCAT2, pairsR1D2, maxR1D2);
          }

          if (true == computeR1R2) {
            logger.info() << "R1R2 pairs: ";
            twoPoint->countPairs(*randCat, *randCat2, RCAT1, RCAT2, pairsR1R2, maxR1R2);
          }
        }
      }

      // Clear random catalog data structures
      twoPoint->clearDataStruct(RCAT1);
      if (twoPoint->statIsRec()) {
        twoPoint->clearDataStruct(SCAT1);
      }

      if (twoPoint->statIsCross()) {
        twoPoint->clearDataStruct(RCAT2);
        if (twoPoint->statIsRec()) {
          twoPoint->clearDataStruct(SCAT2);
        }
      }

    } // Split loop ends

    // close files
    dataCat->closeFile();
    randCat->closeFile();
    if (twoPoint->statIsRec()) {
      recCat->closeFile();
    }

    if (twoPoint->statIsCross()) {
      dataCat2->closeFile();
      randCat2->closeFile();
      if (twoPoint->statIsRec()) {
        recCat2->closeFile();
      }
    }

    // Clear data catalog data structures
    twoPoint->clearDataStruct(DCAT1);
    if (twoPoint->statIsCross()) {
      twoPoint->clearDataStruct(DCAT2);
    }

    // set up names for correlation and pairs fits files
    std::string statistic = params.find<string>("2PCF/statistics");
    std::string correlationFitsName = workdir.string() + "/" + twoPoint->getFitsName(statistic);

     //integrated 1D correlation
    std::string correlationFitsNameInt;
    if (twoPoint->statIs1D()) {
      correlationFitsNameInt = "";
    }
    std::string projectedStat = "PROJECTED";
    if (twoPoint->statIs2DCART()) {
      correlationFitsNameInt = workdir.string() + "/" + twoPoint->getFitsName(projectedStat);
    }
    std::string multipolesStat = "MULTIPOLES";
    if (twoPoint->statIs2DPOL()) {
      correlationFitsNameInt = workdir.string() + "/" + twoPoint->getFitsName(multipolesStat);
    }
    std::string pairsFitsName = workdir.string() + "/" + twoPoint->getPairsFitsName(statistic);

    // ----- Compute correlation
    std::vector<double> correlation;

    if (twoPoint->statIsAuto()) {
      if (twoPoint->statIsRec()) {
        correlation = twoPoint->computeCorrelation(pairsDD, pairsDS, pairsSS, pairsRR, maxDD, maxDS, maxSS, maxRR);
      } else {
        correlation = twoPoint->computeCorrelation(pairsDD, pairsDR, pairsRR, maxDD, maxDR, maxRR);
      }
    }
    if (twoPoint->statIsCross()) {
      if (twoPoint->statIsRec()) {
        correlation = twoPoint->computeCorrelation(pairsD1D2, pairsD1S2, pairsS1D2, pairsS1S2, pairsR1R2,
               maxD1D2, maxD1S2, maxS1D2, maxS1S2, maxR1R2);
      } else {
        correlation = twoPoint->computeCorrelation(pairsD1D2, pairsD1R2, pairsR1D2, pairsR1R2,
               maxD1D2, maxD1R2, maxR1D2, maxR1R2);
      }
    }

    // ----- Write pair file and correlation file
    //Catalogs are needed for header info
    if (twoPoint->statIsAuto()) {
      if (twoPoint->statIsRec()) {
        twoPoint->writePairs(pairsFitsName, pairsDD, pairsDS, pairsSS, pairsRR, maxDD, maxDS, maxSS, maxRR,
           *dataCat, *recCat, *randCat, cosmology);

        twoPoint->writeCorrelation(correlationFitsName, correlationFitsNameInt, correlation,
           *dataCat, *recCat, *randCat, cosmology);
      } else {
        twoPoint->writePairs(pairsFitsName, pairsDD, pairsDR, pairsRR, maxDD, maxDR, maxRR,
            *dataCat, *randCat, cosmology);

        twoPoint->writeCorrelation(correlationFitsName, correlationFitsNameInt, correlation,
            *dataCat, *randCat, cosmology);
      }
    }

    if (twoPoint->statIsCross()) {
      if (twoPoint->statIsRec()) {
        twoPoint->writePairs(pairsFitsName, pairsD1D2, pairsD1S2, pairsS1D2, pairsS1S2, pairsR1R2,
           maxD1D2, maxD1S2, maxS1D2, maxS1S2, maxR1R2,
           *dataCat, *recCat, *randCat, *dataCat2, *recCat2, *randCat2, cosmology);

        twoPoint->writeCorrelation(correlationFitsName, correlationFitsNameInt, correlation,
           *dataCat, *recCat, *randCat, *dataCat2, *recCat2, *randCat2, cosmology);
      } else {
        twoPoint->writePairs(pairsFitsName, pairsD1D2, pairsD1R2, pairsR1D2, pairsR1R2,
           maxD1D2, maxD1R2, maxR1D2, maxR1R2,
           *dataCat, *randCat, *dataCat2, *randCat2, cosmology);

        twoPoint->writeCorrelation(correlationFitsName, correlationFitsNameInt, correlation,
           *dataCat, *randCat, *dataCat2, *randCat2, cosmology);
      }
    }


    // ---- Compute JSON file containing the names of the PF FITS output files
    std::string outputJSON = workdir.native() + "/" + args["euclid_results"].as<std::string>();
    logger.info() << "Writing json file: " << outputJSON;

    std::fstream outfile;
    // open and write JSON file
    outfile.open (outputJSON.c_str(), std::fstream::out);
    if (twoPoint->statIsAuto()) {
      if (twoPoint->statIsRec()) {
        if (twoPoint->statIs2DCART()) {
          outfile << "{\"ReconPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"ReconCorrelationFile2Dcart\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"ReconCorrelationFile2Dproj\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else if (twoPoint->statIs2DPOL()) {
          outfile << "{\"ReconPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"ReconCorrelationFile2Dpolar\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"ReconCorrelationFile2Dmulti\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else {
          outfile << "{\"ReconPairsFile\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"ReconCorrelationFile\" : \"" << correlationFitsName <<"\"";
          outfile << "}";
        }
      } else {
         if (twoPoint->statIs2DCART()) {
          outfile << "{\"AutoPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"AutoCorrelationFile2Dcart\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"AutoCorrelationFile2Dproj\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else if (twoPoint->statIs2DPOL()) {

          outfile << "{\"AutoPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"AutoCorrelationFile2Dpolar\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"AutoCorrelationFile2Dmulti\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else {
          outfile << "{\"AutoPairsFile\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"AutoCorrelationFile\" : \"" << correlationFitsName <<"\"";
          outfile << "}";
        }
      }
    } else if (twoPoint->statIsCross()) {
      if (twoPoint->statIsRec()) {
        if (twoPoint->statIs2DCART()) {
          outfile << "{\"CrossReconPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"CrossReconCorrelationFile2Dcart\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"CrossReconCorrelationFile2Dproj\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else if (twoPoint->statIs2DPOL()) {
          outfile << "{\"CrossReconPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"CrossReconCorrelationFile2Dpolar\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"CrossReconCorrelationFile2Dmulti\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else {
          outfile << "{\"CrossReconPairsFile\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"CrossReconCorrelationFile\" : \"" << correlationFitsName <<"\"";
          outfile << "}";
        }
      } else {
        if (twoPoint->statIs2DCART()) {
          outfile << "{\"CrossPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"CrossCorrelationFile2Dcart\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"CrossCorrelationFile2Dproj\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else if (twoPoint->statIs2DPOL()) {
          outfile << "{\"CrossPairsFile2D\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"CrossCorrelationFile2Dpolar\" : \"" << correlationFitsName <<"\"";
          outfile << ",\"CrossCorrelationFile2Dmulti\" : \"" << correlationFitsNameInt <<"\"";
          outfile << "}";
        } else {
          outfile << "{\"CrossPairsFile\" : \"" << pairsFitsName <<"\"";
          outfile << ",\"CrossCorrelationFile\" : \"" << correlationFitsName <<"\"";
          outfile << "}";
        }
      }
    }
    // close JSON file
    outfile.close();

    // log time duration
    auto te = std::chrono::high_resolution_clock::now();
    logger.info() << "TwoPointCorrelation took " << std::chrono::duration<double>(te - ts).count();

    return Elements::ExitCode::OK;
  } // end main method

};  // end Elements TwoPointCorrelation class

// main call for TwoPointCorrelationFunction
MAIN_FOR(LE3_GC_ComputeTwoPointCorrelation)
