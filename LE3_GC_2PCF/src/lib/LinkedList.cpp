/**
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      src/lib/LinkedList.cpp
 * @date      01/01/18
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#include <LE3_GC_2PCF/LinkedList.h>

#include "LE3_GC_Libraries/Galaxy.h"
#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"

#include <chrono>
#include <numeric>
#include <iomanip>

#include <omp.h>
#include <boost/sort/sort.hpp>

using le3gc::Parameters;
using le3gc::Catalog;
using le3gc::Cosmology;
using le3gc::Galaxy;
using le3gc::dVector;
using le3gc::iVector;

using std::vector;
using std::chrono::duration;
using std::chrono::high_resolution_clock;

namespace le3gc {
  namespace LE3_GC_2PCF {

  // Empty constructor
  LinkedList::LinkedList()
    : TwoPointCorrelation(),
      m_linkData1({}),
      m_linkData2({}),
      m_linkRand1({}),
      m_linkRand2({}),
      m_linkRec1({}),
      m_linkRec2({})
  {/* */}

// -------------------------------------------------------------------------- //

  // Constructor from paremeters object
  LinkedList::LinkedList(const Parameters& param) : TwoPointCorrelation(param) 
  {
    // must be set to 0
    m_box.nobjects = 0;
  }

// -------------------------------------------------------------------------- //

  // Prepare box size and counting scales
  void LinkedList::SetupPairCount() 
  {
    // get starting time
    auto ts = high_resolution_clock::now();

    logger.debug() << "    Largest bounding box:";
    logger.debug() << " \tmin:  \t"  << std::setprecision(7) << m_box.posMin;
    logger.debug() << " \tmax:  \t"  << std::setprecision(7) << m_box.posMax;

    // define max scale to search for pairs
    if (statIs1D() || statIs2DPOL()) {
      // 1D cases use the DIM1 maximum scale
      m_box.maxScale = m_max1D;
    } else if (statIs2DCART()) {
      // 2DCART cases use DIM1 and DIM2 as components of max scale
      m_box.maxScale = std::sqrt(std::pow(m_max1D, 2) + std::pow(m_max2D, 2));
    }

    // get grid from max scale
    double scaleCell = 1.0;
    m_box.defaultBoxCell = m_box.maxScale/scaleCell;
    dVector nC = ((m_box.posMax - m_box.posMin)/m_box.defaultBoxCell);
    m_box.nCell << std::ceil(nC[0]), std::ceil(nC[1]), std::ceil(nC[2]), 1;

    // guess nObjects per cell with default maxScale from biggest catalog
    double nObject = m_box.nobjects/m_box.nCell.prod();
    
    // search optimal scale factor for grid to have about 100 objects per cell on average
    int64_t maxcell = 10e7;

    while (nObject > 100) {
      scaleCell += 1.0;
      m_box.defaultBoxCell = m_box.maxScale/scaleCell;
      nC = ((m_box.posMax - m_box.posMin)/m_box.defaultBoxCell);
      m_box.nCell << std::ceil(nC[0]), std::ceil(nC[1]), std::ceil(nC[2]), 1;
      
      for (int i=0; i<3; i++) if (m_box.nCell[i]==0) m_box.nCell[i] = 1;
      nObject = m_box.nobjects/m_box.nCell.prod();
    }
    
    // reduce the number of cells if above maxcell
    while ((m_box.nCell.prod() > maxcell) && (scaleCell > 1.)) {
      logger.warn() << "Too many grid cells " << m_box.nCell.prod() << " reducing...";
      
      scaleCell -= 1.0;
      logger.debug() << "    New rescaling "<< scaleCell;
      
      m_box.defaultBoxCell = m_box.maxScale/scaleCell;
      nC = ((m_box.posMax - m_box.posMin)/m_box.defaultBoxCell);
      m_box.nCell << std::ceil(nC[0]), std::ceil(nC[1]), std::ceil(nC[2]), 1;
    }
    
    // get final cell number and size
    m_box.sizeCell << m_box.defaultBoxCell, m_box.defaultBoxCell, m_box.defaultBoxCell, 1;
    m_box.rSizeCell << 1.0/m_box.defaultBoxCell, 1.0/m_box.defaultBoxCell, 1.0/m_box.defaultBoxCell, 1;

    logger.debug() << "    cell size: " << std::setprecision(7) << m_box.sizeCell;
    logger.debug() << "    number of cells: " << std::setprecision(7) << m_box.nCell;
    logger.info()  << "    total cells: " << std::setprecision(7) << m_box.nCell.prod();

    // set scales for pair counting
    m_scales = m_getScales();
    
    auto te = high_resolution_clock::now();
    logger.debug() << "    time: " << duration<double>(te - ts).count() << " s";
 }

 // -------------------------------------------------------------------------- //

  // Create the linked-list-like structure for catalog
  void LinkedList::Indicize(Catalog& cat, InputCatType catType) 
  {
    auto ts = high_resolution_clock::now();

    // identify the catalog type
    logger.info() << "Creating linked-list for " << cat.getTypeName() << " catalog";
    
    // number of objects in the catalog
    logger.debug() << "    indexing objects: " << cat.size();

    // quantities for indexing
    const int nynz = m_box.nCell[1]*m_box.nCell[2];
    const int nz = m_box.nCell[2];

    // initialize vector containing  [start,stop) ranges of objects for each grid cell
    vector<size_t> indexes(m_box.nCell.prod() + 1, 0);

    // count object number per grid cell
    for (size_t i=0; i<cat.size(); ++i) {

      // get cell index i,j,k from object distance referred to min position
      // i = (X - X_min)/X_cell_size
      iVector idx = (cat[i].getPos() - m_box.posMin).cwiseProduct(m_box.rSizeCell).cast<int>();

      // check cell number boundaries: >0 and < box.ncell
      idx = idx.cwiseMax(0).cwiseMin((m_box.nCell.array()-1).matrix());

      // get unique index for the i,j,k cell
      size_t indexCell = (idx[0]*nynz + idx[1]*nz + idx[2]);

      // add object to the current indexCell cell (shifted +1)
      indexes[indexCell + 1]++;
    }

    // get [start,stop) ranges per cell by summing up indexes values
    // from absolute [0,2,1,7,3...] to cumulate [0,2,3,10,13...]
    std::partial_sum(std::begin(indexes), std::end(indexes), std::begin(indexes));

    auto te = high_resolution_clock::now();
    logger.debug() << "    indexing time: " << duration<double>(te - ts).count() << " s";

    // sort catalog objects according to cell index
    boost::sort::block_indirect_sort(cat.begin(), cat.begin() + cat.size(), [&](Galaxy i1, Galaxy i2) {
	// first
	iVector ipos1 = (i1.getPos() - m_box.posMin).cwiseProduct(m_box.rSizeCell).cast<int>();
	// check cell number boundaries: >0 and < box.ncell
	ipos1 = ipos1.cwiseMax(0).cwiseMin((m_box.nCell.array()-1).matrix());
	// get unique index for the i,j,k cell
	size_t idx1 ( ipos1[0]*nynz + ipos1[1]*nz + ipos1[2] );
	
	// second
	iVector ipos2 = (i2.getPos() - m_box.posMin).cwiseProduct(m_box.rSizeCell).cast<int>();
	// check cell number boundaries: >0 and < box.ncell
	ipos2 = ipos2.cwiseMax(0).cwiseMin((m_box.nCell.array()-1).matrix());
	// get unique index for the i,j,k cell
	size_t idx2 ( ipos2[0]*nynz + ipos2[1]*nz + ipos2[2] );
	
	// compare
	return idx1 < idx2; }
      );

    GetLinkedList(catType).swap(indexes);
    
    // log time
    te = high_resolution_clock::now();
    logger.debug() << "    sorting time: " << duration<double>(te - ts).count() << " s";
  }

// -------------------------------------------------------------------------- //

  // Compute pair count between catalogs - The counts must be pre-initialized
  void LinkedList::countPairs(Catalog& cat1, Catalog& cat2,
                              InputCatType catType1, InputCatType catType2,
                              vector<double> &pairCounts, double &pairMax, bool allPairs)
  {
    logger.info() << "    allPairs mode: " << allPairs;
    
    // store start time
    auto ts = high_resolution_clock::now();
    
    // compute pair counts normalisation
    TwoPointCorrelation::updateTotalPairs(cat1, cat2, pairMax, allPairs);
    
    // select linked lists according to input catalogs type
    vector<size_t>& link1 = GetLinkedList(catType1);
    vector<size_t>& link2 = GetLinkedList(catType2);

    // initialize pair counts vector
    if (pairCounts.size()==0) pairCounts.resize(m_nBin1D*m_nBin2D,0);

    // define number of steps in the grid to search for pairs
    int steps = static_cast<int>(ceil(m_box.maxScale/m_box.defaultBoxCell));
    logger.debug() << "    search region size: " << steps;

    // pair counting loop
    const int nynz = m_box.nCell[1]*m_box.nCell[2];
    const int nz = m_box.nCell[2];

    const size_t xcellLim = m_box.nCell[0];
    const size_t ycellLim = m_box.nCell[1];
    const size_t zcellLim = m_box.nCell[2];

    // set weights
    double useWeight = (true == m_useWeight) ? 1.0 : 0.0;

    #pragma omp declare reduction(vect : std::vector<double> : std::transform(omp_out.begin(), omp_out.end(), omp_in.begin(), omp_out.begin(), std::plus<double>())) \
      initializer(omp_priv = decltype(omp_orig)(omp_orig.size()))

    #pragma omp parallel for reduction(vect : pairCounts) num_threads(omp_get_max_threads()) schedule(dynamic) collapse(3)
    for (int ic=0; ic<m_box.nCell[0]; ++ic) {
      for (int jc=0; jc<m_box.nCell[1]; ++jc) {
	for (int kc=0; kc<m_box.nCell[2]; ++kc) {
	  
	  // get cat1 cell index
	  const size_t idx1 = static_cast<size_t>(ic*nynz + jc*nz + kc);
	  
	  // check current cat1 cell not empty
	  if ((idx1 >= link1.size()-1) || link1[idx1] == link1[idx1 + 1]) {
	    continue;
	  }
	  
	  // loop on cat2 sub-box cells around cat1 cell position i,j,k
	  for (int iL= -steps; iL<=steps; ++iL) {
	    // exclude cells out of grid (i direction)
	    if (static_cast<size_t>(iL+ic) >= xcellLim) {
	      continue;
	    }
	    
	    for (int jL= -steps; jL<=steps; ++jL) {
	      // exclude cells out of grid (j direction)
	      if (static_cast<size_t>(jL+jc) >= ycellLim) {
		continue;
	      }
	      
	      for (int kL= -steps; kL<=steps; ++kL) {
		// exclude cells out of grid (k direction)
		if (static_cast<size_t>(kL+kc) >= zcellLim) {
		  continue;
		}
		
		// index of cat2 sub-box cell
		const size_t idx2 = iL*nynz + jL*nz + kL + idx1;
		
		// check index in range(link has length idx+1) and cat2 cell not empty
		if ((idx2 >= link2.size()-1) || (link2[idx2 + 1] == link2[idx2])) {
		  continue;
		}
		
		// loop all cell1 elements
		for (size_t i=link1[idx1]; i<link1[idx1 + 1]; ++i) {
		  
		  Galaxy& gal1 = cat1[i];
		  
		  // loop all cell2 elements
		  for (size_t j=link2[idx2]; j<link2[idx2 + 1]; ++j) {
		    
		    // if same catalog couples, skip elements <i
		    if ((j < (i + 1)) && (!allPairs)) {
		      continue;
		    }
		    
		    Galaxy& gal2 = cat2[j];
		    TwoPointCorrelation::fillPairCounts(gal1, gal2, pairCounts, useWeight);
		    
		    // end loop on cat 2 sub-cell
		  }
		  // end loop on cat 1
		}
		// end kL loop
	      }
	      // end jL loop
	    }
	    //end iL loop
	  }
	  // end k
	}
	// end j
      }
      // end i
    }
    
    auto te = high_resolution_clock::now();
    logger.info() << "    time: " << duration<double>(te - ts).count() << " s";
  }

// ---------------------------------------------------------------------------//

  // Clear data structure
  void LinkedList::clearDataStruct(InputCatType catType)
  {
    if (InputCatType::DATA == catType || InputCatType::DATA1 == catType) {
      m_linkData1.clear();
      return;
    }
    if (InputCatType::DATA2 == catType) {
      m_linkData2.clear();
      return;
    }
    if (InputCatType::RAND == catType || InputCatType::RAND1 == catType) {
      m_linkRand1.clear();
      return;
    }
    if (InputCatType::RAND2 == catType) {
      m_linkRand2.clear();
      return;
    }
    if (InputCatType::REC == catType || InputCatType::REC1 == catType) {
      m_linkRec1.clear();
      return;
    }
    if (InputCatType::REC2 == catType) {
      m_linkRec2.clear();
      return;
    }
    throw std::runtime_error("Unrecognized catalogue type");
  }

// -------------------------------------------------------------------------- //

  // Return a pointer to structure containing organized objects
  vector<size_t>& LinkedList::GetLinkedList(const InputCatType& catType) 
  {
    if (InputCatType::DATA == catType || InputCatType::DATA1 == catType) {
      return m_linkData1;
    }
    if (InputCatType::DATA2 == catType) {
      return m_linkData2;
    }
    if (InputCatType::RAND == catType || InputCatType::RAND1 == catType) {
      return m_linkRand1;
    }
    if (InputCatType::RAND2 == catType) {
      return m_linkRand2;
    }
    if (InputCatType::REC == catType || InputCatType::REC1 == catType) {
      return m_linkRec1;
    }
    if (InputCatType::REC2 == catType) {
      return m_linkRec2;
    }
    throw std::runtime_error("Unrecognized catalogue type");
  }

// -------------------------------------------------------------------------- //

} /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */
