/*
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      src/lib/TwoPointCorrelation.cpp
 * @date      01/01/18
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#include "LE3_GC_2PCF/TwoPointCorrelation.h"

#include "LE3_GC_Libraries/Utils.h"
#include "LE3_GC_Libraries/Fits.h"
#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"
#include "LE3_GC_Libraries/Cosmology.h"
#include "LE3_GC_2PCF/LinkedList.h"
#include "LE3_GC_2PCF/KdTree.h"
#include "LE3_GC_2PCF/OcTree.h"

#include <boost/algorithm/string.hpp>
#include <fstream>
#include <iomanip>
#include <algorithm>

using std::string;
using std::vector;
using std::unique_ptr;
using std::chrono::duration;
using std::chrono::high_resolution_clock;

using le3gc::Parameters;
using le3gc::Catalog;
using le3gc::Cosmology;
using le3gc::Fits;
using le3gc::dVector;
using le3gc::iVector;

using le3gc::LE3_GC_2PCF::LinkedList;
using le3gc::LE3_GC_2PCF::KdTree;
using le3gc::LE3_GC_2PCF::OcTree;

namespace le3gc {
 namespace LE3_GC_2PCF {
  
  dVector posMin;
  dVector posMax;

  dVector sizeCell;
  dVector rSizeCell;

  iVector nCell;

  double defaultBoxCell;
  double maxScale;
  double nobjects;
  // Empty constructor
  TwoPointCorrelation::TwoPointCorrelation()
    : m_scales({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      m_box({{0.0, 0.0, 0.0, 0.0}, {0.0, 0.0, 0.0, 0.0},
             {0.0, 0.0, 0.0, 0.0}, {0.0, 0.0, 0.0, 0.0},
	     {0, 0, 0, 0}, 0.0, 0.0, 0.0, false}),
      m_scale1D(le3gc::BinningType::NONE),
      m_scale2D(le3gc::BinningType::NONE),
      m_statistics(CorrelationType::NONE),
      m_nBin1D(0),
      m_min1D(0.0),
      m_max1D(0.0),
      m_nBin2D(0),
      m_min2D(0.0),
      m_max2D(0.0),
      m_piMax(0.0),
      m_useWeight(false),
      m_useTXT(false),
      m_method(""),
      m_nsplit(0),
      m_mirroring(false),
      m_losDef(0),
      m_release("")
  {/* */}

    // -------------------------------------------------------------------------- //
    // Constructor from Parameters
    TwoPointCorrelation::TwoPointCorrelation(const Parameters& param)
      : TwoPointCorrelation() {
      
      // collect all the 2pcf parameters
      m_getParameters(param);
    }
    
    // -------------------------------------------------------------------------- //
    // Read 2PCF pairs results from FITS file according to Euclid 2PCF-GC Data Model
    vector<double> TwoPointCorrelation::readPairs(const string& fileName,
						  const string& pairType,
						  double &maxPairs) 
    {
      vector<double> pairs;
      // open file
      Fits infile(fileName, FitsMode::READ);
      
      // go to table
      infile.gotoTable("PAIRS");
      pairs = infile.getColumn<double>(pairType, 0, std::numeric_limits<int>::max());
      
      // read maxPairs from keyword
      string mkey;
      if (pairType.size()==2) {
	    mkey = "PAIR_" + pairType;
      } else  {
	    mkey = "PAIR" + pairType;
      }
      maxPairs = infile.getKey<double>(mkey);

      infile.closeFits();
      
      return pairs;
    }

    // -------------------------------------------------------------------------- //
    // Read 2PCF pairs results from FITS file according to Euclid 2PCF-GC Data Model.
    void TwoPointCorrelation::readPairs(const string& fileName,
					const string& pairType,
					std::vector<double> &pairs,
					double &maxPairs,
					bool compute,				  
					size_t refSize)
    {
      if (compute) return;

      logger.info() << "Reading "<< pairType << " pairs: ";
      
      Fits infile(fileName, FitsMode::READ);
      
      // go to table
      infile.gotoTable("PAIRS");
      pairs = infile.getColumn<double>(pairType, 0, std::numeric_limits<int>::max());
      
      // read maxPairs from keyword
      string mkey;
      if (pairType.size()==2) {
	    mkey = "PAIR_" + pairType;
      } else  {
	    mkey = "PAIR" + pairType;
      }
      
      maxPairs = infile.getKey<double>(mkey);
      
      if (refSize>0 && pairs.size() != refSize) {
	    throw std::runtime_error(pairType+" size does not match the expected value");
      }
      infile.closeFits(); 
    }
    
    // -------------------------------------------------------------------------- //

    // Write 2PCF pairs results to FITS file according to Euclid 2PCFGC Datamodel
    void TwoPointCorrelation::writePairs(const string& fileName,
					                     const vector<double>& pairs1,
					                     const vector<double>& pairs2,
					                     const vector<double>& pairs3,
					                     const vector<double>& pairs4,
					                     const vector<double>& pairs5,
					                     const double max1,
					                     const double max2,
					                     const double max3,
					                     const double max4,
					                     const double max5,
					                     const Catalog& cat1,
					                     const Catalog& cat2,
					                     const Catalog& cat3,
					                     const Catalog& cat4,
					                     const Catalog& cat5,
					                     const Catalog& cat6,
					                     const Cosmology& cosmo)
    {
      // open and overwrite file
      Fits outfile(fileName, FitsMode::WRITE);
      
      // table name for pair files
      string tableName = "PAIRS";
      
      // add table
      outfile.gotoTable(tableName);
      
      if (statIs1D()) {
	    outfile.addColumn("SCALE", getAxisScale(AxisType::DIM1), "Mpc/h");
      }

      if (statIs2D()) {
	    // 2D cases
	    // get combined scales
	    vector<double> scale1 = getAxisScale(AxisType::DIM1);
	    vector<double> scale2 = getAxisScale(AxisType::DIM2);
	    
	    // create dim1+dim2 mixed scales
	    vector<double> rad1;
	    vector<double> rad2;
	    
	    // loop on DIM1,DIM2 bins
	    for (int k=0; k<m_nBin1D*m_nBin2D; ++k) {
	      int i = k/m_nBin2D;
	      int j = k-i*m_nBin2D;
	      rad1.emplace_back(scale1[i]);
	      rad2.emplace_back(scale2[j]);
	    }

	    // write scales in the first and second column
	    outfile.addColumn("SCALE_1D", rad1, "Mpc/h");
	    outfile.addColumn("SCALE_2D", rad2, "Mpc/h");
      }
    
      if (statIsAutoRec()) {
        outfile.addColumn("DD", pairs1);
        outfile.addColumn("DS", pairs2);
        outfile.addColumn("SS", pairs3);
	    outfile.addColumn("RR", pairs4);
      }

      if (statIsAutoNoRec()) {
        outfile.addColumn("DD", pairs1);
        outfile.addColumn("DR", pairs2);
        outfile.addColumn("RR", pairs3);
      }
      
      if (statIsCrossRec()) {
        outfile.addColumn("D1D2", pairs1);
        outfile.addColumn("D1S2", pairs2);
	    outfile.addColumn("S1D2", pairs3);
        outfile.addColumn("S1S2", pairs4);
	    outfile.addColumn("R1R2", pairs5);
      }

      if (statIsCrossNoRec()) {
        outfile.addColumn("D1D2", pairs1);
        outfile.addColumn("D1R2", pairs2);
        outfile.addColumn("R1D2", pairs3);
	    outfile.addColumn("R1R2", pairs4);
      }
      
      // add identifier keywords
      outfile.setKey("TELESCOP",  "EUCLID");
      outfile.setKey("INSTRUME",  "LE3GC");
      outfile.setKey("FILENAME",  fileName.substr(fileName.find_last_of('/') + 1));
      outfile.setKey("RUNTYPE" ,  "undefined"  , "Type of clustering measurement");
      if (statIsCross()) {
          outfile.setKey("SELECT1"  ,  "undefined"  , "Identifier for the SEL-ID selection");
          outfile.setKey("SELECT2"  ,  "undefined"  , "Identifier for the SEL-ID selection");
      } else {
          outfile.setKey("SELECT"  ,  "undefined"  , "Identifier for the SEL selection performed");
      }
      outfile.setKey("BLINDING",  " "  , "Flag that identifies products that have been blinded for scientific analysis");
      
      outfile.closeFits();
      
      // add catalog keywords
      writeCatalogInfo(fileName, tableName, cat1, cat2, cat3, cat4, cat5, cat6);
      
      // add keywords <2PCF related>
      // add 2PCF keywords
      std::string stat;
      if (statIs1D()) {
          stat = "ONE_DIM";
      } else if (statIs2DCART()) {
          stat = "TWO_DIM_CART";
      } else if (statIs2DPOL()) {
          stat = "TWO_DIM_POL";
      }
      writeInfoToFITS(fileName, tableName, stat);
      
      // add max pairs
      writePairsInfoToFITS(fileName, tableName, max1, max2, max3, max4, max5);
      
      // add keywords <Cosmology related>
      cosmo.writeIDInfoToFITS(fileName, tableName);
      
      logger.info() << "File " <<  fileName.substr(fileName.find_last_of('/') + 1);
      
      // If enabled, write ASCII file results
      if (true == m_useTXT) {
	    string filename = fileName.substr(0, fileName.find_last_of('.')) + ".txt";
	    writePairsASCII(filename, pairs1, pairs2, pairs3, pairs4, pairs5);
      }
    }

    // -------------------------------------------------------------------------- //
    void TwoPointCorrelation:: writeCatalogInfo(const string& fileName,
						                        const string& tableName,
						                        const Catalog& cat1,
						                        const Catalog& cat2,
						                        const Catalog& cat3,
						                        const Catalog& cat4,
						                        const Catalog& cat5,
						                        const Catalog& cat6)
    {
      if (statIsAutoRec()) {
        writeCatInfoToFITS(fileName, tableName, "D", cat1);
        writeCatInfoToFITS(fileName, tableName, "S", cat2);
	    writeCatInfoToFITS(fileName, tableName, "R", cat3);
      }

      if (statIsAutoNoRec()) {
        writeCatInfoToFITS(fileName, tableName, "D", cat1);
        writeCatInfoToFITS(fileName, tableName, "R", cat2);
      }

      if (statIsCrossRec()) {
        writeCatInfoToFITS(fileName, tableName, "D1", cat1);
        writeCatInfoToFITS(fileName, tableName, "S1", cat2);
        writeCatInfoToFITS(fileName, tableName, "R1", cat3);
        writeCatInfoToFITS(fileName, tableName, "D2", cat4);
	    writeCatInfoToFITS(fileName, tableName, "S2", cat5);
        writeCatInfoToFITS(fileName, tableName, "R2", cat6);
      }

      if (statIsCrossNoRec()) { 
        writeCatInfoToFITS(fileName, tableName, "D1", cat1);
        writeCatInfoToFITS(fileName, tableName, "R1", cat2);
        writeCatInfoToFITS(fileName, tableName, "D2", cat3);
        writeCatInfoToFITS(fileName, tableName, "R2", cat4);
      }
    }

    // -------------------------------------------------------------------------- //
    // Write pair counts on plain ASCII file
    void TwoPointCorrelation::writePairsASCII(const string& fileName, 
					                          const vector<double>& pairs1,
					                          const vector<double>& pairs2,
					                          const vector<double>& pairs3,
					                          const vector<double>& pairs4,
					                          const vector<double>& pairs5)
    {
      // open output file
      std::ofstream outputf(fileName.c_str());
      
      // set output options on decimal places
      outputf << std::setiosflags(std::ios::fixed) << std::setprecision(3);
      
      if (statIs1D()) {    
	    vector<double> scale1 = getAxisScale(AxisType::DIM1);
	    
	    // loop and write all pairs
	    if (statIsAutoNoRec()) {
	      for(size_t i = 0; i < pairs1.size(); ++i) {
	        outputf << scale1[i] << " "
	    	        << pairs1[i] << " "
	    	        << pairs2[i] << " "
                    << pairs3[i] << std::endl;
	      }
	    }

	    if (statIsAutoRec() || statIsCrossNoRec()) {
	      for(size_t i = 0; i < pairs1.size(); ++i) {
	        outputf << scale1[i] << " "
		            << pairs1[i] << " "
		            << pairs2[i] << " "
		            << pairs3[i] << " "
		            << pairs4[i]<< std::endl;
	      }
	    }

	    if (statIsCrossRec()) {
	      for(size_t i = 0; i < pairs1.size(); ++i) {
	        outputf << scale1[i] << " "
                    << pairs1[i] << " "
                    << pairs2[i] << " "
                    << pairs3[i] << " "
                    << pairs4[i] << " "
                    << pairs5[i] << std::endl;
	      }
	    }
      }
      
      if (statIs2D()) {
	    // get combined scales
	    vector<double> sca1 = getAxisScale(AxisType::DIM1);
	    vector<double> sca2 = getAxisScale(AxisType::DIM2);
	  
	    // create dim1+dim2 mixed scales
	    vector<double> scac1;
	    vector<double> scac2;
	  
	    // loop on DIM1,DIM2 bins to write value triplets bin1/bin2/correlation
	    for (int k=0; k<m_nBin1D*m_nBin2D; ++k) {
	      int i = k/m_nBin2D;
	      int j = k-i*m_nBin2D;
	      scac1.emplace_back(sca1[i]);
	      scac2.emplace_back(sca2[j]);
	    }
	  
	    // loop and write all pairs
	    if (statIsAutoNoRec()) {
	      for(size_t i = 0; i < pairs1.size(); ++i) {
	        outputf << scac1[i] << " "
	    	        << scac2[i] << " "
	    	        << pairs1[i] << " "
	    	        << pairs2[i] << " "
	    	        << pairs3[i] << std::endl;
	      }
	    }

	    if (statIsAutoRec() || statIsCrossNoRec()) {
	      for(size_t i = 0; i < pairs1.size(); ++i) {
	        outputf << scac1[i] << " "
	  	            << scac2[i] << " "
	  	            << pairs1[i] << " "
	  	            << pairs2[i] << " "
	  	            << pairs3[i] << " "
	  	            << pairs4[i] << std::endl;
	      }
	    }

	    if (statIsCrossRec()) {
	      for(size_t i = 0; i < pairs1.size(); ++i) {
	        outputf << scac1[i] << " "
	  	            << scac2[i] << " "
	  	            << pairs1[i] << " "
	  	            << pairs2[i] << " "
	  	            << pairs3[i] << " "
	  	            << pairs4[i] << " "
	  	            << pairs5[i] << std::endl;
	      }
	    }
      }
      
      // close file
      outputf.close();
      
      // log
      logger.info() << "File " << fileName.substr(fileName.find_last_of('/') + 1);
    }
    
    // -------------------------------------------------------------------------- //
    // Compute correlation. Output vector length depends on correlation Type requested
    // Generalized routine that takes maximum DD,RR,DR pair counts as input
    vector<double> TwoPointCorrelation::computeCorrelation( const vector<double>& counts1,
                                                            const vector<double>& counts2,
                                                            const vector<double>& counts3,
							                                const vector<double>& counts4,
							                                const vector<double>& counts5,
							                                const double max1,
							                                const double max2,
							                                const double max3,
							                                const double max4,
							                                const double max5)
    {
      logger.info() << "Computing correlation";
      
      // ----- correlation for all statistics
      
      // m_nBin2D == 1 in all 1D statistics (AUTO_1D)
      vector<double> correlation(m_nBin1D*m_nBin2D, 0.0);
      
      // Auto non-reconstructed: pairs DD, DR, RR
      if (statIsAutoNoRec()) {
	    for (size_t i=0; i<correlation.size(); ++i) {
	      correlation[i] = counts3[i]>0 ? (counts1[i]/max1 - 2*counts2[i]/max2)*max3/counts3[i] + 1.0 : 0;
	    }
      }
      
      // Cross non-reconstructed: pairs D1D2, D1R2, R1D2, R1R2
      if (statIsCrossNoRec()) { 
	    for (size_t i=0; i<correlation.size(); ++i) {
	      correlation[i] = counts4[i]>0 ? (counts1[i]/max1 - counts2[i]/max2 - counts3[i]/max3)*max4/counts4[i] + 1.0 : 0;
	    }
      }
      
      if (statIsAutoRec()) {
	    // Auto reconstructed: pairs DD, DS, SS, RR
	    for (size_t i=0; i<correlation.size(); ++i) {
	      correlation[i] = counts4[i]>0 ? (counts1[i]/max1 - 2*counts2[i]/max2 +counts3[i]/max3)*max4/counts4[i] : 0;
	    }
      }
      
      // Cross reconstructed: pairs D1D2, D1S2, S1D2, S1S2, R1R2
      if (statIsCrossRec()) { 
	    for (size_t i=0; i<correlation.size(); ++i) {
	      correlation[i] = counts5[i]>0 ? (counts1[i]/max1 - counts2[i]/max2 - counts3[i]/max3 +counts4[i]/max4)*max5/counts5[i] : 0;
	    }
      }
      
      // ----- special finalization for projected case (2D-->1D)
      if (statIs2DCART()) {
	    // DIM 2 bin step
	    double binsz = (m_max2D - m_min2D)/static_cast<double>(m_nBin2D);
	    
	    // temporary 1-D projected correlation vector
	    vector<double> tmpCorrelation(m_nBin1D, 0.0);
	    
	    // split loop on dim1 and dim2
	    for (int k=0; k<m_nBin1D*m_nBin2D; ++k) {
          int i = k/m_nBin2D;
          int j = k-i*m_nBin2D;
	    
          double pi = m_min2D + (j + 0.5)*binsz;
          double xi = correlation[k];
	    
          if (pi <= m_piMax) {
            tmpCorrelation[i] += 2.0*binsz*xi;
          }
        }
	    // swap finalized correlation vector with initial correlation
	    correlation.insert(correlation.end(),tmpCorrelation.begin(),tmpCorrelation.end());
      }
      
      // ----- special finalization for multipole case only ----- //
      if (statIs2DPOL()) {
	    double binsz = (m_max2D - m_min2D)/static_cast<double>(m_nBin2D);
	
	    // multipole 0,1,2,3,4 correlation vector: length is 5*correlation
	    vector<double> correlationMultipole(5*m_nBin1D, 0.0);
	
	    // split loop on dim1 and dim2
	    for (int k=0; k<m_nBin1D*m_nBin2D; ++k) {
	  
          int i = k/m_nBin2D;
          int j = k-i*m_nBin2D;
	  
          double mu = m_min2D + (j + 0.5)*binsz;	  
          double xi = correlation[k];
	  
          if (m_min2D >= 0) { //For backward compatibility: double integral from 0 to 1 
	        if ( xi >= -1.0 ) {
              // monopole
	          correlationMultipole[i] += 0.5*binsz*xi*2.0;
	        
	          // quadrupole
	          correlationMultipole[i + 2*m_nBin1D] += 2.5*binsz*xi*(3.0*pow(mu, 2) - 1.0)*0.5*2.0;
            
	          // hexadecapole
	          correlationMultipole[i + 4*m_nBin1D] += 4.5*binsz*xi*(35.0*pow(mu, 4) - 30.0*pow(mu, 2) + 3.0)*0.125*2.0;
	        }
          } else {
	        if ( xi >= -1.0) {
              // monopole
              correlationMultipole[i] += 0.5*binsz*xi;
	        
	          // dipole
	          correlationMultipole[i + m_nBin1D] += 1.5*binsz*xi*mu;
	        
	          // quadrupole
	          correlationMultipole[i + 2*m_nBin1D] += 2.5*binsz*xi*(3.0*pow(mu, 2) - 1.0)*0.5;
	        
	          // octupole
	          correlationMultipole[i + 3*m_nBin1D] += 3.5*binsz*xi*(5.0*pow(mu, 3) - 3.0*mu)*0.5;
            
	          // hexadecapole
	          correlationMultipole[i + 4*m_nBin1D] += 4.5*binsz*xi*(35.0*pow(mu, 4) - 30.0*pow(mu, 2) + 3.0)*0.125;
	        }
          }
	    }
	
	    // insert multipole correlation vector in final correlation vector
	    correlation.insert(correlation.end(),correlationMultipole.begin(),correlationMultipole.end());
      }
      
      return correlation;
    }
    
    // ---------------------------------------------------------------------------//
    
    // write correlation on FITS file according to Euclid Datamodel
    void TwoPointCorrelation::writeCorrelation(const string& fileName,
					                           const string& fileNameInt,
					                           const vector<double>& correlation,
					                           const Catalog& cat1,
					                           const Catalog& cat2,
					                           const Catalog& cat3,
					                           const Catalog& cat4,
					                           const Catalog& cat5,
					                           const Catalog& cat6,
					                           const Cosmology& cosmo)
    {
      string tableName = "CORRELATION";
      
      // open FITS file
      Fits outfile(fileName, FitsMode::WRITE);
      
      // add table
      outfile.gotoTable(tableName);
      
      // fill table according to correlation type
      if (statIs1D()) {
	    // --- one-dimensional case --- //
	    // scale on 1st column
	    outfile.addColumn("SCALE", getAxisScale(AxisType::DIM1), string("Mpc/h"));
	    // correlation on 2nd column
	    outfile.addColumn("XI", correlation);
      }
      
      if (statIs2D()) {
	    // --- two-dimensional cases --- //
	    // create vectors for 2D scales
	    vector<double> sep1;
	    vector<double> sep2;
	    
	    // get scales on both axis
	    vector<double> scales1 = getAxisScale(AxisType::DIM1);
	    vector<double> scales2 = getAxisScale(AxisType::DIM2);
	    
	    // loop on DIM1,DIM2 bins to write value triplets bin1/bin2/correlation
	    for (int k=0; k<m_nBin1D*m_nBin2D; ++k) {
	      int i = k/m_nBin2D;
	      int j = k-i*m_nBin2D;
	      sep1.emplace_back(scales1[i]);
	      sep2.emplace_back(scales2[j]);
	    }
	    
	    // 1D scale on 1st column
	    outfile.addColumn("SCALE_1D", sep1, "Mpc/h");
	    
	    // 2D scale on 2nd column
	    outfile.addColumn("SCALE_2D", sep2, (statIs2DCART() ? "Mpc/h" : ""));
	    
	    // correlation on 3rd column
	    vector<double>tmp(correlation.begin(), correlation.begin() + m_nBin1D*m_nBin2D);
	    outfile.addColumn("XI", tmp);
      }
      
      // finalization for projected and multipoles
      if (statIs2DPOL()) {
	    // --- Multipole case: need 6x scale dim1 --- //
	    // open FITS file
	    Fits outfileInt(fileNameInt, FitsMode::WRITE);
	    // add table
	    outfileInt.gotoTable(tableName);
	    
	    vector<double> vec = getAxisScale(AxisType::DIM1);
	    outfileInt.addColumn("SCALE", vec, string("Mpc/h"));
	    
	    // correlation on 2, 3, 4, 5, 6 column
	    vector<double>tmp0(correlation.begin() + m_nBin1D*m_nBin2D, correlation.begin() + m_nBin1D*m_nBin2D + m_nBin1D);
	    vector<double>tmp1(correlation.begin() + m_nBin1D*m_nBin2D + m_nBin1D, correlation.begin() + m_nBin1D*m_nBin2D + 2*m_nBin1D);
	    vector<double>tmp2(correlation.begin() + m_nBin1D*m_nBin2D + 2*m_nBin1D, correlation.begin() + m_nBin1D*m_nBin2D + 3*m_nBin1D);
	    vector<double>tmp3(correlation.begin() + m_nBin1D*m_nBin2D + 3*m_nBin1D, correlation.begin() + m_nBin1D*m_nBin2D + 4*m_nBin1D);
	    vector<double>tmp4(correlation.begin() + m_nBin1D*m_nBin2D + 4*m_nBin1D, correlation.begin() + m_nBin1D*m_nBin2D + 5*m_nBin1D);
	    
	    outfileInt.addColumn("XI0", tmp0);
	    outfileInt.addColumn("XI1", tmp1);
	    outfileInt.addColumn("XI2", tmp2);
	    outfileInt.addColumn("XI3", tmp3);
	    outfileInt.addColumn("XI4", tmp4);
	    outfileInt.closeFits();
      }
      if (statIs2DCART()) {// 2DCART
	    // --- Projected case --- //
	    // open FITS file
	    Fits outfileInt(fileNameInt, FitsMode::WRITE);
	    // add table
	    outfileInt.gotoTable(tableName);
	    
	    // scale on 1st column
	    outfileInt.addColumn("SCALE", getAxisScale(AxisType::DIM1), string("Mpc/h"));
	    // correlation on 2nd column
	    vector<double>tmpp(correlation.begin() + m_nBin1D*m_nBin2D, correlation.end());
	    outfileInt.addColumn("XI", tmpp);
	    
	    outfileInt.closeFits();
      }
      
      // add standard keywords
      outfile.setKey("TELESCOP",  "EUCLID");
      outfile.setKey("INSTRUME",  "LE3GC");
      outfile.setKey("FILENAME",  fileName.substr(fileName.find_last_of('/') + 1));
      outfile.setKey("RUNTYPE" ,  "undefined"  , "Type of clustering measurement");
      if (statIsCross()) {
          outfile.setKey("SELECT1"  ,  "undefined"  , "Identifier for the SEL-ID selection");
          outfile.setKey("SELECT2"  ,  "undefined"  , "Identifier for the SEL-ID selection");
      } else {
          outfile.setKey("SELECT"  ,  "undefined"  , "Identifier for the SEL selection performed");
      }
      outfile.setKey("BLINDING",  " "  , "Flag that identifies products that have been blinded for scientific analysis");
      outfile.closeFits();
      
      // add catalog keywords
      writeCatalogInfo(fileName, tableName, cat1, cat2, cat3, cat4, cat5, cat6);
      
      std::string stat;
      if (statIs1D()) {
          stat = "ONE_DIM";
      } else if (statIs2DCART()) {
          stat = "TWO_DIM_CART";
      } else if (statIs2DPOL()) {
          stat = "TWO_DIM_POL";
      }
      writeInfoToFITS(fileName, tableName, stat);
      
      // add cosmology keywords
      cosmo.writeInfoToFITS(fileName, tableName);
      
      logger.info() << "File " << fileName.substr(fileName.find_last_of('/')+1);
      
      if (statIs2D()) {
	    // open FITS file
	    Fits outfileInt(fileNameInt, FitsMode::APPEND);
	    
	    // add table
	    outfileInt.gotoTable(tableName);
	    
	    // add standard keywords
	    outfileInt.setKey("TELESCOP",  "EUCLID");
	    outfileInt.setKey("INSTRUME",  "LE3GC");
	    outfileInt.setKey("FILENAME",  fileNameInt.substr(fileNameInt.find_last_of('/') + 1));
        outfileInt.setKey("RUNTYPE" ,  "undefined"  , "Type of clustering measurement");
        if (statIsCross()) {
          outfileInt.setKey("SELECT1"  ,  "undefined"  , "Identifier for the SEL-ID selection");
          outfileInt.setKey("SELECT2"  ,  "undefined"  , "Identifier for the SEL-ID selection");
        } else {
          outfileInt.setKey("SELECT"  ,  "undefined"  , "Identifier for the SEL selection performed");
        }
        outfileInt.setKey("BLINDING",  " "  , "Flag that identifies products that have been blinded for scientific analysis");
	    
	    outfileInt.closeFits();
	    
	    // add catalog keywords
	    writeCatalogInfo(fileNameInt, tableName, cat1, cat2, cat3, cat4, cat5, cat6);
        
        if (statIs2DCART()) {
            stat = "PROJECTED";
        } else if (statIs2DPOL()) {
            stat = "MULTIPOLE";
        }
        writeInfoToFITS(fileNameInt, tableName, stat);
	    
	    if (statIs2DPOL()) {
	      // open FITS file
	      Fits outfile2(fileNameInt, FitsMode::APPEND);
	    
	      // add table
	      outfile2.gotoTable(tableName);
	    
	      // LOS keyword
	      string los = (m_losDef == 0) ? "mid-point" : (m_losDef == 1) ? "bisector" : (m_losDef == 2) ? "end-point" : "unknown";
	      outfile2.setKey("LOS_DEF", los, "Line of sight definition");
	    
	      outfile2.closeFits();
	    }
	    
	    // add cosmology keywords
	    cosmo.writeInfoToFITS(fileNameInt, tableName);
	    
	    logger.info() << "File " << fileNameInt.substr(fileNameInt.find_last_of('/')+1);
      }
      
      // write correlation on ASCII file if requested
      if (true == m_useTXT) {
	    string file1 = fileName.substr(0, fileName.find_last_of('.')) + ".txt";
	    string file2 = fileNameInt.substr(0, fileNameInt.find_last_of('.')) + ".txt";
	    writeCorrelationASCII(correlation, file1, file2);
      }
    }

    // ---------------------------------------------------------------------------//

    // Write two point correlation on file
    void TwoPointCorrelation::writeCorrelationASCII(const vector<double>& correlation,
						                            const string& fileName,
						                            const string& fileNameInt)
    {
      if (statIs1D()) {
	    // --- ONE dimension case --- //
	    
	    // open file
	    std::ofstream outputf(fileName.c_str());
	    
	    // set output precision
	    outputf << std::setiosflags(std::ios::fixed) << std::setprecision(6);
	    
	    // get scale on DIM1 axis
	    vector<double> rad = getAxisScale(AxisType::DIM1);
	    
	    // loop on DIM1 bins to write value couples bin/correlation
	    for (int i=0; i<m_nBin1D; ++i) {
	      outputf << rad[i] << "   " << correlation[i] << std::endl;
	    }

	    // close file
	    outputf.close();
	    logger.info() << "File " << fileName.substr(fileName.find_last_of('/') + 1);
      }
      
      if (statIs2D()) {
	    // --- TWO dimension cases ---- //
	    
	    // open file
	    std::ofstream outputf(fileName.c_str());
	    
	    // set output precision
	    outputf << std::setiosflags(std::ios::fixed) << std::setprecision(6);
	    
	    // get scales on both axis
	    vector<double> rad1 = getAxisScale(AxisType::DIM1);
	    vector<double> rad2 = getAxisScale(AxisType::DIM2);
	    
	    // loop on DIM1,DIM2 bins to write value triplets bin1/bin2/correlation
	    for (int k=0; k<m_nBin1D*m_nBin2D; ++k) {
	      int i = k/m_nBin2D;
	      int j = k-i*m_nBin2D;
	      outputf << rad1[i] << "   " << rad2[j] << "   " << correlation[k] << std::endl;
	    }

	    // close file
	    outputf.close();
	    logger.info() << "File " << fileName.substr(fileName.find_last_of('/') + 1);
      }
            
      // finalization for projected and multipoles
      if (statIs2DPOL()) {
	    // open file
	    std::ofstream outputfi(fileNameInt.c_str());

	    // set output precision
	    outputfi << std::setiosflags(std::ios::fixed) << std::setprecision(6);
	    
	    // get scale on DIM1 axis
	    vector<double> rad = getAxisScale(AxisType::DIM1);
	    
	    // --- Multipole case, 6 columns--- //
	    // loop on DIM1 bins to write value quadruplets bin/pole0/pole2/pole4
	    for (int i=0; i<m_nBin1D; ++i) {
	      outputfi << rad[i] << "\t" << correlation[m_nBin1D*m_nBin2D + i]
                             << "\t" << correlation[m_nBin1D*m_nBin2D + i + m_nBin1D]
                             << "\t" << correlation[m_nBin1D*m_nBin2D + i + 2*m_nBin1D]
                             << "\t" << correlation[m_nBin1D*m_nBin2D + i + 3*m_nBin1D]
                             << "\t" << correlation[m_nBin1D*m_nBin2D + i + 4*m_nBin1D] << std::endl;
	    }
	    
	    outputfi.close();
	    logger.info() << "File " << fileNameInt.substr(fileNameInt.find_last_of('/') + 1);
      }
      
      if (statIs2DCART()) {
	    // open file
	    std::ofstream outputfi(fileNameInt.c_str());
 
	    // set output precision
	    outputfi << std::setiosflags(std::ios::fixed) << std::setprecision(6);
	    
	    // get scale on DIM1 axis
	    vector<double> rad = getAxisScale(AxisType::DIM1);
	    
	    // --- Projected case --- //
	    // loop on DIM1 bins to write value couples bin/correlation
	    for (int i=0; i<m_nBin1D; ++i) {
	      outputfi << rad[i] << "   " << correlation[m_nBin1D*m_nBin2D+i] << std::endl;
	    }
	    
	    outputfi.close();
	    logger.info() << "File " << fileNameInt.substr(fileNameInt.find_last_of('/') + 1);
      }
    }
    
    // -------------------------------------------------------------------------- //

    // write catalog info in FITS file as keywords
    void TwoPointCorrelation::writeCatInfoToFITS(const std::string& fileName, 
						                         const std::string& tableName,
						                         const std::string& typeName,
						                         const Catalog& cat)
    {
      // open EXISTING FITS file in APPEND mode
      Fits outfile(fileName, FitsMode::APPEND);

      // add table
      outfile.gotoTable(tableName);
      
      // add keywords
      outfile.setComment(" ");
      outfile.setComment("----------- " + typeName + " Catalog ----------");
      outfile.setComment(" ");
      
      outfile.setKey(typeName + "_NAME", cat.getCatalogName(), "Name of the catalog");
      outfile.setKey(typeName + "_ID", cat.getCatalogId(), "Catalog selection ID");
      outfile.setKey(typeName + "_NOBJ", cat.getTotalElementsNumber(), "Number of objects in the catalog");
      
      // close FITS file
      outfile.closeFits();
    }
    
    // -------------------------------------------------------------------------- //
    
    // write 2PCF configuration on FITS file
    void TwoPointCorrelation::writeInfoToFITS(const string& fileName,
					                          const string& tableName,
                                              std::string& statistics)
    {
      // open existing FITS file
      Fits outfile(fileName, FitsMode::APPEND);

      // add table
      outfile.gotoTable(tableName);
      
      // add keywords
      outfile.setComment(" ");
      outfile.setComment("----------- Correlation Parameters ----------");
      outfile.setComment(" ");
      
      outfile.setKey("STAT", statistics, "2PCF Related Keywords Statistics of 2PCF (e.g. ONE_DIM)");
      // partition method [LINKED_LIST, KD_TREE]
      outfile.setKey("METHOD", m_method, "Pair counting method [KD_TREE, LINKED_LIST]");
      
      if (statIs1D()) {
	    // keywords for 1D statistics
	    outfile.setKey("BIN_TYPE", m_validScales.right.at(m_scale1D),
	    	           "Scale type [LINear, LOGarithmic]");
	    outfile.setKey("BIN_NUM", m_nBin1D, "Number of bins");
	    outfile.setKey("BIN_MIN", m_min1D, "Minimum separation [Mpc/h]");
	    outfile.setKey("BIN_MAX", m_max1D, "Maximum separation [Mpc/h]");
      }
      if (statIs2D()) {
	    // keywords for 2D statistics
	    outfile.setKey("BIN1TYPE", m_validScales.right.at(m_scale1D),
	    	           "Scale type dim 1 [LINear, LOGarithmic]");
	    outfile.setKey("BIN1NUM", m_nBin1D, "Number of bins dim 1");
	    outfile.setKey("BIN1MIN", m_min1D, "Minimum separation dim 1 [Mpc/h]");
	    outfile.setKey("BIN1MAX", m_max1D, "Maximum separation dim 1 [Mpc/h]");
	    outfile.setKey("BIN2TYPE", m_validScales.right.at(m_scale2D),
	    	           "Scale type dim 2 [LINear, LOGarithmic]");
	    outfile.setKey("BIN2NUM", m_nBin2D, "Number of bins dim 2");
	    if (statIs2DCART()) {
	      outfile.setKey("BIN2MIN", m_min2D, "Minimum separation dim 2 [Mpc/h]");
	      outfile.setKey("BIN2MAX", m_max2D, "Maximum separation dim 2 [Mpc/h]");
          outfile.setKey("PI_MAX",  m_piMax, "Maximum separation for projection");
	    }
	    if (statIs2DPOL()) {
	      outfile.setKey("BIN2MIN", std::abs(m_min2D), "Minimum separation dim 2 (mu)");
	      outfile.setKey("BIN2MAX", m_max2D, "Maximum separation dim 2 (mu)");
	    }
      }
      
      string useWeight = m_useWeight ? "true" : "false";
      outfile.setKey("WEIGHT", useWeight, "Use objects weight [true, false]");
      
      outfile.setKey("SPLIT", m_nsplit, "Split factor");
      
      outfile.closeFits();
    }
    
    // ---------------------------------------------------------------------------//

    // write total pairs configuration on FITS file
    void TwoPointCorrelation::writePairsInfoToFITS(const string& fileName,
						                           const string& tableName,
						                           const double max1,
						                           const double max2,
						                           const double max3,
						                           const double max4,
						                           const double max5)
    {
      // open existing FITS file
      Fits outfile(fileName, FitsMode::APPEND);

      // add table
      outfile.gotoTable(tableName);
      
      if (statIsAuto()) {
	    if (statIsRec()) {
	      outfile.setKey("PAIR_DD", max1, "Total weighted number of DD pairs");
	      outfile.setKey("PAIR_DS", max2, "Total weighted number of DS pairs");
	      outfile.setKey("PAIR_SS", max3, "Total weighted number of SS pairs");
	      outfile.setKey("PAIR_RR", max4, "Total weighted number of RR pairs");
	    }
	    else {
	      outfile.setKey("PAIR_DD", max1, "Total weighted number of DD pairs");
	      outfile.setKey("PAIR_DR", max2, "Total weighted number of DR pairs");
	      outfile.setKey("PAIR_RR", max3, "Total weighted number of RR pairs");
	    }
      }
      
      if (statIsCross()) {
	    if (statIsRec()) {
	      outfile.setKey("PAIRD1D2", max1, "Total weighted number of D1D2 pairs");
	      outfile.setKey("PAIRD1S2", max2, "Total weighted number of D1S2 pairs");
	      outfile.setKey("PAIRS1D2", max3, "Total weighted number of S1D2 pairs");
	      outfile.setKey("PAIRS1S2", max4, "Total weighted number of S1S2 pairs");
	      outfile.setKey("PAIRR1R2", max5, "Total weighted number of R1R2 pairs");
	    }
	    else {
	      outfile.setKey("PAIRD1D2", max1, "Total weighted number of D1D2 pairs");
	      outfile.setKey("PAIRD1R2", max2, "Total weighted number of D1R2 pairs");
	      outfile.setKey("PAIRR1D2", max3, "Total weighted number of R1D2 pairs");
	      outfile.setKey("PAIRR1R2", max4, "Total weighted number of R1R2 pairs");
	    }
      }
      
      outfile.closeFits();
    }
    
    // -------------------------------------------------------------------------- //

    // Compute the two point correlation scale values for the specified dimension
    vector<double> TwoPointCorrelation::getAxisScale(const AxisType& ax) 
    {
      // load values for DIM1
      double max        = m_max1D;
      double min        = m_min1D;
      int nBin          = m_nBin1D;
      BinningType scale = m_scale1D;
      
      // or overwrite with DIM2 if
      if (AxisType::DIM2 == ax) {
	    max   = m_max2D;
	    min   = m_min2D;
	    nBin  = m_nBin2D;
	    scale = m_scale2D;
      }
      
      int i = -1;
      vector<double> rad(nBin, 0.0);
      
      // linear scale OR log scale filling
      if (BinningType::LIN == scale) {
	    // get bin step
	    double bin = (max - min)/static_cast<double>(nBin);

	    // fill vector i*step + 0.5*step + min
	    std::generate_n(std::begin(rad), nBin,
			[&](){i++; return ((i + 0.5)*bin + min);});
	
      } else if (BinningType::LOG == scale) {
	    // get bin step
	    double bin = std::log10(max/min)/static_cast<double>(nBin);

	    // re-define min as log(min)
	    min = std::log10(min);

	    // fill vector i*step + 0.5*step + min
	    std::generate_n(std::begin(rad), nBin,
			[&](){i++; return std::pow(10.0, ((i + 0.5)*bin + min));});
      }
      
      return rad;
    }
    
    // ---------------------------------------------------------------------------//
    
    // factory method to build the requested pair counting method
    unique_ptr<TwoPointCorrelation> TwoPointCorrelation::make2PCF(const Parameters& params) 
    {
      // conversion to uppercase
      string uname (boost::to_upper_copy(params.find<string>("2PCF/method")));
      
      // get correct pointer to selected method
      unique_ptr<TwoPointCorrelation> corr (nullptr);
      if (uname == "LINKED_LIST") {
	    corr.reset(new LinkedList(params));
      } else if (uname == "KD_TREE") {
	    corr.reset(new KdTree(params));
      } else if (uname == "OCTREE") {
	    corr.reset(new OcTree(params));
      } else {
	    std::runtime_error("WRONG counting method: " + uname);
      }
      
      return corr;
    }
    
    // -------------------------------------------------------------------------- //
    
    // Initialize the internal box from input parameters.
    // If not given, the box will be updated using the actual catalogue
    void TwoPointCorrelation::initBox(dVector box_in_min,
				                      dVector box_in_max)
    {
      m_box.posMin = box_in_min;
      m_box.posMax = box_in_max;
      
      dVector boxsize=box_in_max-box_in_min;
      m_box.set_from_params = (boxsize[0]>0 && boxsize[1]>0 && boxsize[2]>0);
      
      //Cell sizes will be set later
      m_box.sizeCell  = {0.0, 0.0, 0.0, 0.0};
      m_box.rSizeCell = {0.0, 0.0, 0.0, 0.0};
      m_box.nCell     = {0, 0, 0, 0};
      
      m_box.defaultBoxCell = 0.0;
      m_box.maxScale = 0.0;
      m_box.nobjects = 0;
    }
    
    // -------------------------------------------------------------------------- //
    
    // Convert dVectors into BoxType
    BoxType TwoPointCorrelation::boxFromVector(dVector box_in_min,
                                               dVector box_in_max,
                                               int nobjects_in) 
    {
      BoxType box;
      box.posMin = box_in_min;
      box.posMax = box_in_max;

      box.set_from_params = false;

      box.sizeCell  = {0.0, 0.0, 0.0, 0.0};
      box.rSizeCell = {0.0, 0.0, 0.0, 0.0};
      box.nCell     = {0, 0, 0, 0};

      box.defaultBoxCell = 0.0;
      box.maxScale = 0.0;
      box.nobjects = nobjects_in;

      return box;
    }
    
    // -------------------------------------------------------------------------- //
    
    void TwoPointCorrelation::updateBox(BoxType box) 
    {
      //Already set from input parameters, do not update
      if (m_box.set_from_params) return;
      
      if (0 < m_box.nobjects) {
	    m_box.posMax = m_box.posMax.cwiseMax(box.posMax);
	    m_box.posMin = m_box.posMin.cwiseMin(box.posMin);
	    m_box.nobjects = std::max(m_box.nobjects, box.nobjects);
      } else  {
	    m_box.posMin = box.posMin;
	    m_box.posMax = box.posMax;
	    m_box.nobjects = box.nobjects;
      }
    }
    
    // -------------------------------------------------------------------------- //

    int TwoPointCorrelation::updateBoundingBox(Catalog &cat, std::string cattype)
    {
      int ndata = cat.getTotalElementsNumber();  
      dVector posmin = cat.getCatalogPosMin();
      dVector posmax = cat.getCatalogPosMax();
      
      logger.info() << "Catalog type " << cattype;
      logger.info() << "  objects available " << ndata;
      logger.info() << "  bounding box: " << posmin;
      logger.info() << "                " << posmax;
      
      BoxType box = boxFromVector(posmin, posmax, ndata);
      updateBox(box);
      
      return ndata;
    }

    // -------------------------------------------------------------------------- //
    
    int TwoPointCorrelation::setSplit(int nData, int nData2, int nRandom, int nRandom2)
    {
      int nd = (statIsCross() ? std::max(nData,nData2) : nData);
      int nr = (statIsCross() ? std::max(nRandom,nRandom2) : nRandom);
      int splitFactor = (nr>1000) ? (nr +nd/10)/nd : 1;
      if (splitFactor==0) splitFactor = 1;
      
      m_nsplit = splitFactor;
      return splitFactor;
    } 
    
    // -------------------------------------------------------------------------- //
    //                             Private Methods
    // -------------------------------------------------------------------------- //
    
    // internal map statistics-name / statistics-type
    boost::bimap<string, CorrelationType> TwoPointCorrelation::m_validStatistics = []() 
    {
      boost::bimap<string, CorrelationType> map;
      map.insert({"AUTO_1D", CorrelationType::AUTO_1D});
      map.insert({"AUTO_2DCART", CorrelationType::AUTO_2DCART});
      map.insert({"AUTO_2DPOL", CorrelationType::AUTO_2DPOL});
      map.insert({"CROSS_1D", CorrelationType::CROSS_1D});
      map.insert({"CROSS_2DCART", CorrelationType::CROSS_2DCART});
      map.insert({"CROSS_2DPOL", CorrelationType::CROSS_2DPOL});
      map.insert({"AUTO_REC_1D", CorrelationType::AUTO_REC_1D});
      map.insert({"AUTO_REC_2DCART", CorrelationType::AUTO_REC_2DCART});
      map.insert({"AUTO_REC_2DPOL", CorrelationType::AUTO_REC_2DPOL});
      map.insert({"CROSS_REC_1D", CorrelationType::CROSS_REC_1D});
      map.insert({"CROSS_REC_2DCART", CorrelationType::CROSS_REC_2DCART});
      map.insert({"CROSS_REC_2DPOL", CorrelationType::CROSS_REC_2DPOL});
      map.insert({"NONE", CorrelationType::NONE});

      return map; //std::move(map);
    }();

    // -------------------------------------------------------------------------- //
    
    // internal map binning-name / binning-type
    boost::bimap<string, le3gc::BinningType> TwoPointCorrelation::m_validScales = []() 
    {
      boost::bimap<string, le3gc::BinningType> map;
      map.insert({"LIN", le3gc::BinningType::LIN});
      map.insert({"LOG", le3gc::BinningType::LOG});

      return map; //std::move(map);
    }();
    
    // -------------------------------------------------------------------------- //
    
    // get input parameters
    void TwoPointCorrelation::m_getParameters(const Parameters& params) 
    {
      // store 2PCF method
      m_method = boost::to_upper_copy(params.find<string>("2PCF/method"));
      
      // --- Get statistics
      std::string statistic = params.find<string>("2PCF/statistics");
      m_statistics = m_getStatistics(statistic);
      logger.info() << "Statistics " << statistic;
      
      // --- Get 1D binning
      logger.info() << "Binning DIM1";

      // bin type [LOG,LIN]
      m_scale1D = m_getScaleType(params.find<string>("2PCF/bin1_type"));

      // bins
      m_nBin1D = params.find<int>("2PCF/bin1_num");
      assert(m_nBin1D > 0);
      logger.info() << "  - bins: " << m_nBin1D;

      // range min,max
      m_min1D = params.find<double>("2PCF/bin1_min");
      m_max1D = params.find<double>("2PCF/bin1_max");
      assert (m_min1D < m_max1D);
      logger.info() << "  - range: " << m_min1D << " to " << m_max1D << " Mpc/h";
      
      if (statIs1D()) {
	    m_nBin2D = 1;
      }
      
      // read 2D parameters only for requested statistics
      if (statIs2DCART()) {
	    logger.info() << "Binning DIM2";
        
	    // bin type [LOG,LIN]
	    m_scale2D = m_getScaleType(params.find<string>("2PCF/bin2_type"));

	    // n bins
	    m_nBin2D = params.find<int>("2PCF/bin2_num");
	    assert(m_nBin2D > 0);
	    logger.info() << "  - bins: " << m_nBin2D;

	    // range min,max
	    m_min2D = params.find<double>("2PCF/bin2_min");
	    m_max2D = params.find<double>("2PCF/bin2_max");
	    assert (m_min2D < m_max2D);
	    logger.info() << "  - range: " << m_min2D << " to " << m_max2D << " Mpc/h";

	    // pi max
	    m_piMax = params.find<double>("2PCF/PI_MAX");
	    logger.info() << "PI max: " << m_piMax << " Mpc/h";
      }
      
      if (statIs2DPOL()) {
	    // set mu range min,max
	    m_min2D = -1;
	    m_max2D = 1;
	    assert (m_min2D >= -1);
	    assert (m_max2D <= 1);

	    // set bin type
	    m_scale2D = le3gc::BinningType::LIN;

	    // n bins
	    m_nBin2D = params.find<int>("2PCF/bin2_num");

	    // los definition
	    m_losDef = params.find<int>("2PCF/los_def",0);
	    
	    // print summary
	    logger.info() << "Binning in mu:";
	    logger.info() << "  - bins: " << m_nBin2D;
	    logger.info() << "  - range: " << m_min2D << " to " << m_max2D;
	    std::string los = (m_losDef == 0) ? "mid-point" : (m_losDef == 1) ? "bisector" : (m_losDef == 2) ? "end-point" : "unknown";
	    logger.info() << "  - los: " << los;
      }
      
      // --- enable/disable catalog weights usage
      m_useWeight = params.find<bool>("2PCF/use_weight");
      logger.info() << "Using catalog weights: " << std::boolalpha << m_useWeight;
      
      // --- number of split
      m_nsplit = params.find<int>("2PCF/split_factor");
      
      // enable/disable ASCII output files
      m_useTXT = params.find<bool>("2PCF/ASCII_out");
      
      // mu>-mu mirroring enabled if auto-correlation and multipoles
      m_mirroring = (statIs2DPOL() && m_min2D < 0);
      
      logger.info();
    }
    
    // ---------------------------------------------------------------------------//

    // check the correlation type requested
    CorrelationType TwoPointCorrelation::m_getStatistics(const string& statName) 
    {
      // upper name to check
      string upName(boost::to_upper_copy<string>(statName));
      
      // check for valid statistics
      if (m_validStatistics.left.end() == m_validStatistics.left.find(upName)) {
	    std::runtime_error("UNKNOWN or WRONG statistics:" + statName);
      }
      
      return m_validStatistics.left.at(upName);
    }
    
    // ---------------------------------------------------------------------------//

    // check the binning type requested
    BinningType TwoPointCorrelation::m_getScaleType(const string& name) 
    {
      // conversion to uppercase
      string upName (boost::to_upper_copy(name));
      
      // check for valid scale type
      if (m_validScales.left.end() == m_validScales.left.find(upName)) {
	    std::runtime_error("UNKNOWN or WRONG scale type: " + name);
      }
      
      return m_validScales.left.at(upName);
    }
    
    // ---------------------------------------------------------------------------//
    
    // get the pair counting reference scales for both KD_TREE and LINKED_LIST
    ScalesType TwoPointCorrelation::m_getScales() 
    {
      // empty initialized scale object
      ScalesType scale = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
      
      // fill scales according to statistics type
      
      // 1D case
      if (statIs1D()) {
	    scale.min1Dsq = pow(m_min1D, 2.0);
	    scale.max1Dsq = pow(m_max1D, 2.0);
	    scale.min1D = m_min1D;
	    scale.max1D = m_max1D;
      }

      // 2D cases
      if (statIs2D()) {
	    scale.min1Dsq = pow(m_min1D, 2.0);
	    scale.max1Dsq = pow(m_max1D, 2.0);
	    scale.min1D = m_min1D;
	    scale.max1D = m_max1D;
	    scale.min2Dsq = pow(m_min2D, 2.0);
	    scale.max2Dsq = pow(m_max2D, 2.0);
	    scale.min2D = m_min2D;
	    scale.max2D = m_max2D;
      }

      // correction for log binning 1D
      if (le3gc::BinningType::LOG == m_scale1D) {
	    scale.min1D = log10(scale.min1D);
	    scale.max1D = log10(scale.max1D);
      }

      // correction for log binning 2D
      if (le3gc::BinningType::LOG == m_scale2D && statIs2D()) {
	    scale.min2D = log10(scale.min2D);
	    scale.max2D = log10(scale.max2D);
      }

      // get inverse bin amplitude
      scale.dim1_binInv = static_cast<double>(m_nBin1D)/(scale.max1D - scale.min1D);
      if (statIs2D()) scale.dim2_binInv = static_cast<double>(m_nBin2D)/(scale.max2D - scale.min2D);
      
      return scale;
    }
    
    // ---------------------------------------------------------------------------//
    
    // Get the catalog enclosing box x, y, z dimensions
    BoxType TwoPointCorrelation::m_getBoxSize(Catalog& cat) 
    {
      // get position of first object as reference min/max
      BoxType box = {cat[0].getPos(), cat[0].getPos(),
		             {0.0, 0.0, 0.0, 0.0}, {0.0, 0.0, 0.0, 0.0},
		             {0, 0, 0, 0}, 0.0, 0.0, 0.0, false};
      
      // loop all objects to search min and max coordinates in the x,y,z directions
      #pragma omp parallel num_threads(omp_get_max_threads())
      {
	    // reference value for each thread
	    dVector threadMax = cat[0].getPos();
	    dVector threadMin = cat[0].getPos();

        #pragma omp for
	    for (size_t i=1; i<cat.size(); ++i) {
          // get object coordinates
          dVector tmp = cat[i].getPos();
        
	      // compare each axis max
          threadMax = threadMax.cwiseMax(tmp);
        
	      // compare each axis min
          threadMin = threadMin.cwiseMin(tmp);
        }

	    // one thread per time compare its max/min against reference
        #pragma omp critical(__GET_BOX_SIZE__)
	    {
          box.posMax = box.posMax.cwiseMax(threadMax);
          box.posMin = box.posMin.cwiseMin(threadMin);
        }
      }

      box.nobjects = static_cast<double>(cat.size());

      logger.debug() << "    axis: \t" << "   X \t" << "    Y \t" << "    Z";
      logger.debug() << "    min:  \t" << std::setprecision(7) << box.posMin;
      logger.debug() << "    max:  \t" << std::setprecision(7) << box.posMax;
      
      return box;
    }

/* ------------------------------------------------------------------- */
// Get the output file base name
  std::string TwoPointCorrelation::getFitsName(std::string& statistics){
    // return "data/EUC_LE3_GCL_2PCF_" + "_" + m_release + "_" ;
    return "data/EUC_LE3_GCL_2PCF_" + m_release + "_Correlation_" + statistics + "_" + le3gc::getDateTimeString() + ".fits";
  }
/* ------------------------------------------------------------------- */

// Get the output file base name
  std::string TwoPointCorrelation::getPairsFitsName(std::string& statistics){
    // return "data/EUC_LE3_GCL_2PCF_" + "_" + m_release + "_" ;
    return "data/EUC_LE3_GCL_2PCF_" + m_release + "_PAIRS_" + statistics + "_" + le3gc::getDateTimeString() + ".fits";
  }

/* ------------------------------------------------------------------- */

    // compute or update total number of pairs
    void TwoPointCorrelation::updateTotalPairs(Catalog &cat1, 
					                           Catalog &cat2,
					                           double &pairMax,
					                           bool cross)
    {
	  size_t ngal1 = cat1.size();
	  size_t ngal2 = cat2.size();
	  
	  if (m_useWeight) {
	    if (cross) {
	      double weight1=cat1.getWeight(ngal1);
          double weight2=cat2.getWeight(ngal2);
          pairMax += weight1*weight2;
        } else {
	      double w1=0,w2=0;
          
          #pragma omp parallel for reduction(+:w1,w2) num_threads(omp_get_max_threads())
          for (size_t i=0; i<ngal1; ++i) {
	        double weight = cat1[i].getWeight();
	        w1 += weight;
	        w2 += weight*weight;
	      }
	      pairMax += (w1*w1-w2)*0.5;
        }
      } else {
	    if (cross) {
	      pairMax += double(ngal1)*ngal2;
	    } else {
	      pairMax += double(ngal1)*(ngal1-1)*0.5;
	    }
      }
    }

    // ------------------------------------------------------------------- //

    // fill the pair counts from given galaxy pair
    void TwoPointCorrelation::fillPairCounts(Galaxy& gal1, 
					   Galaxy& gal2,
					   vector<double>& pairCounts,
	                                   double useWeight)
    {  
      // ----- 1D case ----- //
    
      if (statIs1D()) {
	    // get radial distance (squared) using eigen (4th dimension is 0)
	    double dist = (gal1.getPos() - gal2.getPos()).squaredNorm();
        
	    // check distance (squared) within range (squared)
	    if ((dist > m_scales.max1Dsq) || (dist < m_scales.min1Dsq)) {
	      return;
        }
        
          // get log distance if LOG binning case
        if (BinningType::LIN == m_scale1D) {
	      dist = sqrt(dist);
        } else {
	      dist = 0.5*log10(dist);
        }
        
        // get index
        size_t kk = static_cast<size_t>((dist - m_scales.min1D)*m_scales.dim1_binInv);
        
        // check index within range ( kk >0 and kk < nBin )
        if (kk < static_cast<size_t>(m_nBin1D)) {
	      pairCounts[kk] += 1.0 + useWeight*(gal1.getWeight()*gal2.getWeight() - 1.0);
        }
      }
  
      if (statIs2DCART()) {
        // ----- 2D cases ----- //
      
        double ssqr = (gal2.getPos() - gal1.getPos()).squaredNorm();
        double suma = (gal1.getPos()).squaredNorm() - (gal2.getPos()).squaredNorm();
        double sumb = (gal1.getPos() + gal2.getPos()).squaredNorm();
      
        double dist2 = suma*suma/sumb;  // (r_para)^2
        double dist1 = ssqr - dist2;    // (r_perp)^2
	
        // check ranges on both distances
        if ((dist2 < m_scales.min2Dsq) || (dist2 > m_scales.max2Dsq) ||
          (dist1 < m_scales.min1Dsq) || (dist1 > m_scales.max1Dsq)) {
	      return;
        }
      
        // get dimension 1 separation
        if (BinningType::LIN == m_scale1D) {
	      dist1 = sqrt(dist1);
        } else {
	      dist1 = 0.5*log10(dist1);
        }
      
        // get dimension 1 separation index
        size_t idx1 = static_cast<size_t>(((dist1 - m_scales.min1D)*m_scales.dim1_binInv));
      
        // test index1 in range (idx1>0 and idx1<nBin1D)
        if (idx1 >= static_cast<size_t>(m_nBin1D)) {
	      return;
        }
      
        // get dimension 2 separation
        if (BinningType::LIN == m_scale2D) {
	      dist2 = sqrt(dist2);
        } else {
	      dist2 = 0.5*log10(dist2);
        }
      
        // get dimension 2 separation index
        size_t idx2 = static_cast<size_t>(floor((dist2 - m_scales.min2D)*m_scales.dim2_binInv));
      
        // test index2 in range (idx2>0 and idx2<nBin2D)
        if (idx2 >= static_cast<size_t>(m_nBin2D)) {
	      return;
        }
      
        // add pair weight to the unique i,j position in the pairs separation vector
        pairCounts[idx1*m_nBin2D + idx2] += 1.0 + useWeight*(gal1.getWeight()*gal2.getWeight() - 1.0);
      }

 
      if (statIs2DPOL()) {
        // ----- 2D cases ----- //
      
        dVector los;
        if (m_losDef == 1) los = (gal1.getPos().normalized() + gal2.getPos().normalized()).normalized();
        else if (m_losDef == 2) los = gal1.getPos().normalized();
        else los = (gal1.getPos() + gal2.getPos()).normalized();

        dVector sep = gal2.getPos()-gal1.getPos();
        double dist1 = sep.squaredNorm();
        double mu = los.dot(sep.normalized());

        // for backward compatibility
        if (m_min2D>=0) mu = fabs(mu);

        // check radial distance
        if ((dist1 < m_scales.min1Dsq) || (dist1 > m_scales.max1Dsq)) {
	      return;
        }
      
        // get dimension 1 separation
        if (BinningType::LIN == m_scale1D) {
	      dist1 = sqrt(dist1);
        } else {
	      dist1 = 0.5*log10(dist1);
        }
      
        // get dimension 1 separation index
        size_t idx1 = static_cast<size_t>(((dist1 - m_min1D)*m_scales.dim1_binInv));
      
        // test index1 in range (idx1>0 and idx1<nBin1D)
        if (idx1 >= static_cast<size_t>(m_nBin1D)) {
	      return;
        }

        // get dimension 2 separation index
        size_t idx2 = static_cast<size_t>(((mu - m_min2D)*m_scales.dim2_binInv));
      
        // test index2 in range (idx2>=0 and idx2<nBin2D)
        if (idx2 < static_cast<size_t>(m_nBin2D)) {
           pairCounts[idx1*m_nBin2D + idx2] += 1.0 + useWeight*(gal1.getWeight()*gal2.getWeight() - 1.0);
        }

        // mirroring to -mu for vanishing odd multipoles in the auto-correlation case
        if (m_mirroring) {
          idx2 = static_cast<size_t>(((-mu - m_min2D)*m_scales.dim2_binInv));
      
          if (idx2 < static_cast<size_t>(m_nBin2D)) {
            pairCounts[idx1*m_nBin2D + idx2] += 1.0 + useWeight*(gal1.getWeight()*gal2.getWeight() - 1.0);
          }
        }
      }
    }

    //------------------------------------------------------------------ //

} /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */
