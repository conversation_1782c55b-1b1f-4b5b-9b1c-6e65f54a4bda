/*
 * Copyright (C) 2012-2024 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      src/lib/OcTree.cpp
 * @date      19/02/24
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2012-2024 Euclid Science Ground Segment - GNU General Public License
 */

#include "LE3_GC_2PCF/OcTree.h"

#include "LE3_GC_Libraries/Galaxy.h"
#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"

#include <unordered_map>
#include <chrono>
#include <iomanip>
#include <numeric>

#include <omp.h>
#include <boost/sort/sort.hpp>

#define MAX_DEPTH 10

using le3gc::Parameters;
using le3gc::Catalog;
using le3gc::Cosmology;
using le3gc::Galaxy;

using std::vector;
using std::shared_ptr;
using std::make_shared;
using std::unordered_map;

using std::chrono::duration;
using std::chrono::high_resolution_clock;

namespace le3gc {
  namespace LE3_GC_2PCF {
    
    // Empty constructor
    OcTree::OcTree() : TwoPointCorrelation()
    {
      // initialize the max number of object per node
      m_nMax = 100;

      // initialize sqrCellNum
      for (uint i=0; i<=MAX_DEPTH; i++) m_sqrCellSize[i] = pow(2,-2*double(i));
    }
  
    // -------------------------------------------------------------------------- //
     
    // Constructor from parameters object
    OcTree::OcTree(const Parameters& param) : TwoPointCorrelation(param)
    {
      // initialize the max number of object per node
      m_nMax = 100;

      // initialize sqrCellNum
      for (uint i=0; i<=MAX_DEPTH; i++) m_sqrCellSize[i] = pow(2,-2*double(i));
    }
 
    // -------------------------------------------------------------------------- //
  
    // Prepare counting scales
    void OcTree::SetupPairCount(void)
    {
      // get starting time
      auto ts = high_resolution_clock::now();
      
      logger.debug() << "    Largest bounding box:";
      logger.debug() << " \tmin:  \t"  << std::setprecision(7) << m_box.posMin;
      logger.debug() << " \tmax:  \t"  << std::setprecision(7) << m_box.posMax;

      // set scales for pair count
      m_scales = m_getScales();
      
      auto te = high_resolution_clock::now();
      logger.debug() << "    time: " << duration<double>(te - ts).count() << " s";
    }
 
    // -------------------------------------------------------------------------- //
  
    // Create the OcTree structure for catalog
    void OcTree::Indicize(Catalog& cat, InputCatType catType)
    {
      auto ts = high_resolution_clock::now();

      // identify the catalog type
      logger.info() << "Creating octree for " << cat.getTypeName() << " catalog";
      
      // number of objects in the catalog
      logger.debug() << "    objects: " << cat.size();
      
      // get box size and set tree size
      if (m_size<0) {
	m_origin.assign({m_box.posMin[0], m_box.posMin[1], m_box.posMin[2]});
	m_size = (m_box.posMax - m_box.posMin).maxCoeff();
	for (uint i=0; i<=MAX_DEPTH; i++) m_sqrCellSize[i] *= m_size*m_size;
      }

      logger.debug() << "    tree origin: (" << m_origin[0] << ", " << m_origin[1] << ", " << m_origin[2] << ")";
      logger.debug() << "    tree size: " << m_size;
      logger.debug() << "    maximum elements per leaf " << m_nMax;

      // build tree nodes recursively and insert objects
      m_maxDepth = 0;
      m_createTree(cat, 0, cat.size(), 1, catType);

      logger.debug() << "    maximum depth " << m_maxDepth;

      //log time
      auto te = high_resolution_clock::now();
      logger.debug() << "    time: " << duration<double>(te - ts).count() << " s";
    }

    // ---------------------------------------------------------------------------//

    // create nodes recursively
    void OcTree::m_createTree(Catalog& cat, size_t first, size_t last, uint32_t locCode, InputCatType catType)
    {   
      // initialize new node
      shared_ptr<ocNode> thisNode = make_shared<ocNode>(locCode, 0, first, last);
      const uint32_t depth = (31-__builtin_clz(locCode))/3;

      // split node elements if more than m_nMax
      if ((last - first) > m_nMax) {
	
	// Determine octant centre
	const double l = m_size/pow(2,depth);
	const uint32_t code = locCode - (1<<(3*depth));
	std::vector<double> centre(m_origin);
	for (uint i=0; i<3; i++) centre[i] += (double(m_compact1By2(code >> i)) + 0.5) * l;
	
	// get octant numbers
	std::vector<int> numbers(8,0);
	
	for (size_t i=first; i<last; i++) {
	  Galaxy& gal = cat[i];
	  int octant = 0;
	  
	  if (gal.getPos(0) >= centre[0]) octant |= 1;
	  if (gal.getPos(1) >= centre[1]) octant |= 2;
	  if (gal.getPos(2) >= centre[2]) octant |= 4;

	  numbers[octant]++;
	}

	// get cumulative distribution
	std::partial_sum(numbers.begin(), numbers.end(), numbers.begin());
	
	// sort
	boost::sort::block_indirect_sort(cat.begin() + first, cat.begin() + last, [&](Galaxy i, Galaxy j) {
	    int octant1 = 0;
	    int octant2 = 0;
	    
	    if (i.getPos(0) >= centre[0]) octant1 |= 1;
	    if (i.getPos(1) >= centre[1]) octant1 |= 2;
	    if (i.getPos(2) >= centre[2]) octant1 |= 4;
	    
	    if (j.getPos(0) >= centre[0]) octant2 |= 1;
	    if (j.getPos(1) >= centre[1]) octant2 |= 2;
	    if (j.getPos(2) >= centre[2]) octant2 |= 4;
	    
	    return (octant1 < octant2);
	  });

	// create new level of nodes
	for (uint i=0; i<8; i++) {
	  if (((i==0) && numbers[i]==0) || ((i>0) && numbers[i]==numbers[i-1])) continue;
	  thisNode->childExists |= (1<<i);

	  size_t start;
	  if (i==0) start = first;
	  else start = first + numbers[i-1];
	  
	  m_createTree(cat, start, first+numbers[i], (locCode<<3)|i, catType);
	}
      }

      // put node in hash table
      m_getTree(catType).insert({locCode,thisNode});

      // set maximum depth
      m_maxDepth = std::max(m_maxDepth, depth);
    }

    // ---------------------------------------------------------------------------//

    // Compute pair count between catalogs - The counts must be pre-initialized
    void OcTree::countPairs(Catalog& cat1, Catalog& cat2,
			    InputCatType catType1, InputCatType catType2,
			    vector<double> &pairCounts, double &pairMax, bool allPairs)
    {
      logger.info() << "    allPairs mode: " << allPairs;
      
      // compute pair counts normalisation
      TwoPointCorrelation::updateTotalPairs(cat1, cat2, pairMax, allPairs);
      
      // initialize pair counts vector
      if (pairCounts.size()==0) pairCounts.resize(m_nBin1D*m_nBin2D,0);

      // select octree root according to input catalogs type
      shared_ptr<ocNode> node1 = m_lookupNode(1,catType1);
      shared_ptr<ocNode> node2 = m_lookupNode(1,catType2);
      
      // number of jobs to process in parallel
      std::vector<shared_ptr<ocNode>> nodeList;
      int maxDepth = m_maxDepth;
      m_addNodeList(node1, maxDepth, nodeList, catType1);

      const uint n1 = nodeList.size();
      logger.info() << "    parallel jobs: " << n1;

      // store start time
      auto ts = high_resolution_clock::now();

      // pair counts in parallel
      #pragma omp declare reduction(vect : std::vector<double> : std::transform(omp_out.begin(), omp_out.end(), omp_in.begin(), omp_out.begin(), std::plus<double>())) \
	initializer(omp_priv = decltype(omp_orig)(omp_orig.size()))

      #pragma omp parallel for reduction(vect : pairCounts) num_threads(omp_get_max_threads()) shared(nodeList) schedule(dynamic)
      for (uint i=0; i<n1; ++i) {
          m_countPairsSerial(nodeList[i], node2, cat1, cat2, pairCounts, allPairs, catType1, catType2);
      }
      
      // get time
      auto te = high_resolution_clock::now();
      logger.info() << "    time: " << duration<double>(te - ts).count() << " s";
    }

    // ---------------------------------------------------------------------------//

    // Clear data structure
    void OcTree::clearDataStruct(InputCatType catType)
    {
      if (InputCatType::DATA == catType || InputCatType::DATA1 == catType) {
	m_treeData1.clear();
	return;
      }
      if (InputCatType::DATA2 == catType) {
	m_treeData2.clear();
	return;
      }
      if (InputCatType::RAND == catType || InputCatType::RAND1 == catType) {
	m_treeRand1.clear();
	return;
      }
      if (InputCatType::RAND2 == catType) {
	m_treeRand2.clear();
	return;
      }
      if (InputCatType::REC == catType || InputCatType::REC1 == catType) {
	m_treeRec1.clear();
	return;
      }
      if (InputCatType::REC2 == catType) {
	m_treeRec2.clear();
	return;
      }
      throw std::runtime_error("Unrecognized catalogue type");
    }

    // -------------------------------------------------------------------------- //
    
    // Return pointer to octree
    std::unordered_map<uint32_t, shared_ptr<ocNode>>& OcTree::m_getTree(const InputCatType& catType)
    {
      if (InputCatType::DATA == catType || InputCatType::DATA1 == catType) {
	return m_treeData1;
      }
      if (InputCatType::DATA2 == catType) {
	return m_treeData2;
      }
      if (InputCatType::RAND == catType || InputCatType::RAND1 == catType) {
	return m_treeRand1;
      }
      if (InputCatType::RAND2 == catType) {
	return m_treeRand2;
      }
      if (InputCatType::REC == catType || InputCatType::REC1 == catType) {
	return m_treeRec1;
      }
      if (InputCatType::REC2 == catType) {
	return m_treeRec2;
      }
      throw std::runtime_error("Unrecognized catalogue type");
    }
 
    // -------------------------------------------------------------------------- //
  
    // Compactify interlaced 32bits bitset
    uint32_t OcTree::m_compact1By2(uint32_t x)
    {
      x &= 0x49249249;                  // x = ---- 9--8 --7- -6-- 5--4 --3- -2-- 1--0
      x = (x ^ (x >>  2)) & 0x030c30c3; // x = ---- --98 ---- 76-- --54 ---- 32-- --10
      x = (x ^ (x >>  4)) & 0x0300f00f; // x = ---- --98 ---- ---- 7654 ---- ---- 3210
      x = (x ^ (x >>  8)) & 0xff0000ff; // x = ---- --98 ---- ---- ---- ---- 7654 3210
      x = (x ^ (x >> 16)) & 0x000003ff; // x = ---- ---- ---- ---- ---- --98 7654 3210
      
      return x;
    }
 
    // -------------------------------------------------------------------------- //
  
    // Point to node
    shared_ptr<ocNode> OcTree::m_lookupNode(uint32_t locCode, InputCatType catType)
    {
      const auto iter = m_getTree(catType).find(locCode);
      return (iter == m_getTree(catType).end() ? nullptr : iter->second);
    }

    // -------------------------------------------------------------------------- //
    
    // Create node list for parallel octree method
    void OcTree::m_addNodeList(shared_ptr<ocNode> node,
			       int depthRemain,
			       std::vector<shared_ptr<ocNode>>& nodeList,
			       InputCatType catType)
    {
      if (nullptr != node) {
	if ((depthRemain > 0) && (node->childExists!=0)) {
	  for (uint i=0; i<8; i++) {
	    if (node->childExists&(1<<i)) {
	      const uint32_t locCodeChild = (node->locCode<<3)|i;
	      m_addNodeList(m_lookupNode(locCodeChild, catType), depthRemain-1, nodeList, catType);
	    }
	  }
	} else {
	  nodeList.emplace_back(node);
	}
      }
    }
    
    // -------------------------------------------------------------------------- //
    
    // Function to compute minimum and maximum distances between nodes
    void OcTree::m_distNodes(const shared_ptr<ocNode> node1, const shared_ptr<ocNode> node2, double& min, double& max)
    {
      // compute depths, difference
      uint32_t key1 = node1->locCode;
      uint32_t key2 = node2->locCode;
      int32_t depth1 = node1->depth;
      int32_t depth2 = node2->depth;
      const uint32_t dd = unsigned(std::abs(depth1-depth2));
    
      // find deepest node 
      if (depth2>depth1) {
	std::swap(key1,key2);
	std::swap(depth1,depth2);
      }
      key2 <<= dd*3;
      
      // get node coordinates difference
      int32_t x_diff = (key1 & _X_MASK) - (key2 & _X_MASK);
      int32_t y_diff = (key1 & _Y_MASK) - (key2 & _Y_MASK);
      int32_t z_diff = (key1 & _Z_MASK) - (key2 & _Z_MASK);
      
      uint32_t nxd=dd; 
      uint32_t nyd=dd; 
      uint32_t nzd=dd;

      if (x_diff<0) {x_diff = std::abs(x_diff); nxd=0;};
      if (y_diff<0) {y_diff = std::abs(y_diff); nyd=0;};
      if (z_diff<0) {z_diff = std::abs(z_diff); nzd=0;};
      
      // get coordinates from Morton code 
      const uint32_t code = ((x_diff & _X_MASK) | (y_diff & _Y_MASK) | (z_diff & _Z_MASK));
      const int pX = m_compact1By2(code >> 0);
      const int pY = m_compact1By2(code >> 1);
      const int pZ = m_compact1By2(code >> 2);
      
      // compute node minimum distance
      double pmin[3];
      pmin[0] = pX > (1 << nxd) ? static_cast<double>(pX - (1 << nxd)) : 0.0;
      pmin[1] = pY > (1 << nyd) ? static_cast<double>(pY - (1 << nyd)) : 0.0; 
      pmin[2] = pZ > (1 << nzd) ? static_cast<double>(pZ - (1 << nzd)) : 0.0;

      // Compute node maximum distance
      if (nxd==0) nxd=dd; else nxd=0;
      if (nyd==0) nyd=dd; else nyd=0;
      if (nzd==0) nzd=dd; else nzd=0;
      
      double pmax[3];
      pmax[0] = static_cast<double>(pX + (1 << nxd));
      pmax[1] = static_cast<double>(pY + (1 << nyd));
      pmax[2] = static_cast<double>(pZ + (1 << nzd));

      // Return distances
      const double l2 = m_sqrCellSize[depth1];
      min = (pmin[0]*pmin[0] + pmin[1]*pmin[1] + pmin[2]*pmin[2])*l2;
      max = (pmax[0]*pmax[0] + pmax[1]*pmax[1] + pmax[2]*pmax[2])*l2;
    }
 
    // -------------------------------------------------------------------------- //
   
    // Counting pairs recursive function
    void OcTree::m_countPairsSerial(shared_ptr<ocNode> node1,
				    shared_ptr<ocNode> node2,
				    Catalog& cat1,
				    Catalog& cat2,
				    std::vector<double>& pairCounts,
				    bool allPairs,
				    InputCatType catType1, 
				    InputCatType catType2)
    {
      // Test depth level to reject distant nodes when counting same cat
      if ((node1->locCode < node2->locCode) &&
	  (node1->childExists == 0)  &&
	  (node2->childExists == 0)  &&
	  (allPairs == false)) {
	return;
      }
      
      // Test node minimum and maximum node-node distances
      double dMin, dMax;
      m_distNodes(node1,node2,dMin,dMax);
      
      // case 2D Cartesian
      if (statIs2DCART()) {
	if ( ((0.25*dMin > m_scales.max1Dsq) || (dMax < m_scales.min1Dsq)) &&
	     ((0.25*dMin > m_scales.max2Dsq) || (dMax < m_scales.min2Dsq)) ) {
	  return;
	}
      } else {
	// cases: 1D or 2D Polar
	if ( (dMax < m_scales.min1Dsq) || (dMin > m_scales.max1Dsq) ) {
	  return;
	}
      }

      // if both leaves then count pairs
      if ((node1->childExists == 0) && (node2->childExists == 0)) {
	double useWeight = (true == m_useWeight) ? 1.0 : 0.0;

	// loop over all the elements in the current nodes
	for (size_t i=node1->first; i<node1->last; ++i) {
	  Galaxy& gal1 = cat1[i];
	  for (size_t j=node2->first; j<node2->last; ++j) {
	    // skip equal objects in case of same catalog counting
	    if ((allPairs==false) && (node1->locCode == node2->locCode) && (i <= j)) continue;
	    Galaxy& gal2 = cat2[j];
	    TwoPointCorrelation::fillPairCounts(gal1, gal2, pairCounts, useWeight);
	  }
	}
      } else {
	// delegate counting to sub-nodes
	if (node2->childExists == 0) {
	  for (uint i=0; i<8; i++) {
	    if (node1->childExists&(1<<i)) {
	      const uint32_t locCodeChild = (node1->locCode<<3)|i;
	      m_countPairsSerial(m_lookupNode(locCodeChild,catType1), node2, cat1, cat2, pairCounts, allPairs, catType1, catType2);
	    }
	  }
	} else {
	  for (uint i=0; i<8; i++) {
	    if (node2->childExists&(1<<i)) {
	      const uint32_t locCodeChild = (node2->locCode<<3)|i;
	      m_countPairsSerial(node1, m_lookupNode(locCodeChild,catType2), cat1, cat2, pairCounts, allPairs, catType1, catType2);
	    }
	  }
	}
      }
    }
    
  } /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */
