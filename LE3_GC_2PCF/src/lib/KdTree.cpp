/*
 * Copyright (C) 2012-2020 Euclid Science Ground Segment
 *
 * This library is free software; you can redistribute it and/or modify it under
 * the terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation; either version 3.0 of the License, or (at your option)
 * any later version.
 *
 * This library is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file      src/lib/KdTree.cpp
 * @date      01/01/18
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>> [optimization]
 * <AUTHOR> <<EMAIL>> [integration SDC-IT]
 * @copyright (C) 2012-2020 Euclid Science Ground Segment - GNU General Public License
 */

#include "LE3_GC_2PCF/KdTree.h"

#include "LE3_GC_Libraries/Galaxy.h"
#include "LE3_GC_Libraries/Parameters.h"
#include "LE3_GC_Libraries/Catalog.h"

#include <algorithm>
#include <unordered_map>
#include <chrono>
#include <algorithm>

#include <omp.h>

using le3gc::Parameters;
using le3gc::Catalog;
using le3gc::Cosmology;
using le3gc::Galaxy;

using std::vector;
using std::shared_ptr;
using std::make_shared;
using std::unordered_map;

using std::chrono::duration;
using std::chrono::high_resolution_clock;

namespace le3gc {
  namespace LE3_GC_2PCF {

  // Empty constructor
  KdTree::KdTree() : TwoPointCorrelation() 
  {
    // initialize the max number of object per node
    m_nMax = 100;
  }

  // -------------------------------------------------------------------------- //

  // Constructor from parameters object
  KdTree::KdTree(const Parameters& param) : TwoPointCorrelation(param) 
  {
    // initialize the max number of object per node
    m_nMax = 100;
  }

  // -------------------------------------------------------------------------- //

  // Prepare counting scales
  void KdTree::SetupPairCount() 
  {
    // get starting time
    auto ts = high_resolution_clock::now();

    // set scales for pair count
    m_scales = m_getScales();

    auto te = high_resolution_clock::now();
    logger.debug() << "    time: " << duration<double>(te - ts).count() << " s";
  }

  // -------------------------------------------------------------------------- //

  // Create the KdTree structure for catalog
  void KdTree::Indicize(Catalog& cat, InputCatType catType) 
  {
    auto ts = high_resolution_clock::now();

    // identify the catalog type
    logger.info() << "Creating kd-tree for " << cat.getTypeName() << " catalog";

    // number of objects in the catalog
    logger.debug() << "    objects: " << cat.size();

    // get box size
    BoxType box = m_getBoxSize(cat);

    // Search larger dimension for first split
    int splitDim = 0;
    int dummy_row = 0;

    double dummy = (box.posMax - box.posMin).maxCoeff(&dummy_row, &splitDim);

    logger.debug() << "    tree first split dimension " << splitDim << " [X=0, Y=1, Z=2]";
    logger.debug() << "    first split dimension range " << dummy;
    logger.debug() << "    maximum elements per leaf " << m_nMax;

    // build tree nodes recursively
    m_maxDepth = 0;
    std::vector<double> pMin = {box.posMin[0], box.posMin[1], box.posMin[2]}; 
    std::vector<double> pMax = {box.posMax[0], box.posMax[1], box.posMax[2]};

    m_createTree(cat, 0, cat.size(), splitDim, 1, pMin, pMax, catType);
    
    logger.debug() << "    maximum depth " << m_maxDepth;

    // log time
    auto te = high_resolution_clock::now();
    logger.debug() << "    time: " << duration<double>(te - ts).count() << " s";
  }

  // -------------------------------------------------------------------------- //

  // create nodes recursively
  void KdTree::m_createTree(Catalog& cat,
			    size_t first,
			    size_t last,
			    int splitDim,
			    uint32_t locCode,
			    std::vector<double>& pMin,
			    std::vector<double>& pMax,
			    InputCatType catType)
  {
    // initialize new node
    shared_ptr<Node> thisNode = make_shared<Node>(first, last, splitDim, locCode, pMin, pMax, false);

    // split node elements if more than m_nMax
    if ((last - first) > m_nMax) {
      
      // unset node as leaf
      thisNode->hasChildren = true;
      
      // midpoint
      double min = pMin[splitDim];
      double max = pMax[splitDim];
      double midpoint = min + 0.5*(max-min);

      // partition along splitDim
      auto iMid = std::partition(cat.begin() + first, cat.begin() + last, [&](Galaxy i) { return i.getPos(splitDim) < midpoint; });
      size_t nLeft = iMid - (cat.begin() + first);

      // sliding
      while (nLeft == 0 || nLeft == (last-first)) {
	if (nLeft == 0) {
	  min = max;
	  
	  for (size_t i=first; i<last; i++) {
	    Galaxy& gal = cat[i];
	    double pos = gal.getPos(splitDim);
	    if (pos<min) min = pos;
	  }
	}
	
	if (nLeft == (last-first)) {
	  max = min;
	  
	  for (size_t i=first; i<last; i++) {
	    Galaxy& gal = cat[i];
	    double pos = gal.getPos(splitDim);
	    if (pos>max) max = pos;
	  }
	}
	
	midpoint = min + 0.5*(max-min);

	// partition along splitDim
	iMid = std::partition(cat.begin() + first, cat.begin() + last, [&](Galaxy i) { return i.getPos(splitDim) < midpoint; });
	nLeft = iMid - (cat.begin() + first);
      }
      
      // get new left/right nodes min/max elements position
      std::vector<double> rMin(pMin);
      std::vector<double> lMax(pMax);

      // adjust min max in the split direction
      lMax[splitDim] = midpoint;
      rMin[splitDim] = midpoint;

      // create new level nodes left and right
      int splitDimLeft = 0;
      int splitDimRight = 0;
      double maxLeft = 0;
      double maxRight = 0;

      for (uint i=0; i<3; i++) {
	double deltaLeft = lMax[i]-pMin[i];
	if (deltaLeft > maxLeft) {
	  maxLeft = deltaLeft;
	  splitDimLeft = i;
	}

	double deltaRight = pMax[i]-rMin[i];
	if (deltaRight > maxRight) {
	  maxRight = deltaRight;
	  splitDimRight = i;
	}
      }

      m_createTree(cat, first, first + nLeft, splitDimLeft, (locCode<<1)|0, pMin, lMax, catType);
      m_createTree(cat, first + nLeft, last, splitDimRight, (locCode<<1)|1, rMin, pMax, catType);
    }

    // put node in hash table
    m_getTree(catType).insert({locCode,thisNode});

    // set maximum depth
    m_maxDepth = std::max(m_maxDepth,(31-__builtin_clz(locCode)));
  }

  // ---------------------------------------------------------------------------//

  // Compute pair count between catalogs - The counts must be pre-initialized
  void KdTree::countPairs(Catalog& cat1, Catalog& cat2,
                          InputCatType catType1, InputCatType catType2,
			  vector<double> &pairCounts, double &pairMax, bool allPairs) 
  {
    logger.info() << "    allPairs mode: " << allPairs;

    // compute pair counts normalisation
    TwoPointCorrelation::updateTotalPairs(cat1, cat2, pairMax, allPairs);

    // select kdtree according to input catalogs type
    shared_ptr<Node> node1 = m_lookupNode(1,catType1);
    shared_ptr<Node> node2 = m_lookupNode(1,catType2);

    // initialize pair counts vector
    if (pairCounts.size()==0) pairCounts.resize(m_nBin1D*m_nBin2D,0);

    // number of jobs to process in parallel
    std::vector<shared_ptr<Node>> nodeList;
    int maxDepth = m_maxDepth;
    m_addNodeList(node1, maxDepth, nodeList, catType1);

    const uint n1 = nodeList.size();
    logger.info() << "    parallel jobs: " << n1;

    // store start time
    auto ts = high_resolution_clock::now();

    // pair counts in parallel
    #pragma omp declare reduction(vect : std::vector<double> : std::transform(omp_out.begin(), omp_out.end(), omp_in.begin(), omp_out.begin(), std::plus<double>())) \
      initializer(omp_priv = decltype(omp_orig)(omp_orig.size()))

    #pragma omp parallel for reduction(vect : pairCounts) num_threads(omp_get_max_threads()) shared(nodeList) schedule(dynamic)
    for (uint i=0; i<n1; ++i) {
        m_countPairsSerial(nodeList[i], node2, cat1, cat2, pairCounts, allPairs, catType1, catType2);
    }

    auto te = high_resolution_clock::now();
    logger.info() << "    time: " << duration<double>(te - ts).count() << " s";
  }

  // ---------------------------------------------------------------------------//

  // Clear data structure
  void KdTree::clearDataStruct(InputCatType catType)
  {
    if (InputCatType::DATA == catType || InputCatType::DATA1 == catType) {
      m_treeData1.clear();
      return;
    }
    if (InputCatType::DATA2 == catType) {
      m_treeData2.clear();
      return;
    }
    if (InputCatType::RAND == catType || InputCatType::RAND1 == catType) {
      m_treeRand1.clear();
      return;
    }
    if (InputCatType::RAND2 == catType) {
      m_treeRand2.clear();
      return;
    }
    if (InputCatType::REC == catType || InputCatType::REC1 == catType) {
      m_treeRec1.clear();
      return;
    }
    if (InputCatType::REC2 == catType) {
      m_treeRec2.clear();
      return;
    }
    throw std::runtime_error("Unrecognized catalogue type");
  }

  // -------------------------------------------------------------------------- //

  // Return pointer to structure containing organized objects
  std::unordered_map<uint32_t, shared_ptr<Node>>& KdTree::m_getTree(const InputCatType& catType)
  {
    if (InputCatType::DATA == catType || InputCatType::DATA1 == catType) {
      return m_treeData1;
    }
    if (InputCatType::DATA2 == catType) {
      return m_treeData2;
    }
    if (InputCatType::RAND == catType || InputCatType::RAND1 == catType) {
      return m_treeRand1;
    }
    if (InputCatType::RAND2 == catType) {
      return m_treeRand2;
    }
    if (InputCatType::REC == catType || InputCatType::REC1 == catType) {
      return m_treeRec1;
    }
    if (InputCatType::REC2 == catType) {
      return m_treeRec2;
    }
    throw std::runtime_error("Unrecognized catalogue type");
  }

  // -------------------------------------------------------------------------- //

  // Point to node
  shared_ptr<Node> KdTree::m_lookupNode(uint32_t locCode, InputCatType catType)
  {
    const auto iter = m_getTree(catType).find(locCode);
    return (iter == m_getTree(catType).end() ? nullptr : iter->second);
  }
  
  // -------------------------------------------------------------------------- //

  // Create node list for parallel kd_tree method
  void KdTree::m_addNodeList(shared_ptr<Node> node,
                             int depthRemain,
                             std::vector<shared_ptr<Node>>& nodeList,
                             InputCatType catType) 
  {
    if (nullptr != node) {
      if ((depthRemain > 0) && (node->hasChildren)) {
        m_addNodeList(m_lookupNode((node->locCode<<1)|0, catType), depthRemain-1, nodeList, catType);
        m_addNodeList(m_lookupNode((node->locCode<<1)|1, catType), depthRemain-1, nodeList, catType);
      } else {
        nodeList.emplace_back(node);
      }
    }
  }

  // -------------------------------------------------------------------------- //

  // Function to compute minimum and maximum distances between nodes
  void KdTree::m_distNodes(const shared_ptr<Node> node1, const shared_ptr<Node> node2, double& min, double& max)
  {
    // get minimum distance between nodes 
    min = 0.0;
    
    for (uint i=0; i<3; ++i) {
      if ((node1->pMin[i] > node2->pMax[i]) && (node1->pMin[i] > node2->pMin[i])) {
        min += (node1->pMin[i] - node2->pMax[i])*(node1->pMin[i] - node2->pMax[i]);
      }
      else if ((node2->pMin[i] > node1->pMax[i]) && (node2->pMax[i] > node1->pMax[i])) {
        min += (node1->pMax[i] - node2->pMin[i])*(node1->pMax[i] - node2->pMin[i]);
      }
    }

    // get maximum squared distance between nodes
    max = 0.0;

    for (uint i=0;i<3; ++i) {
      const double dx = std::max(node1->pMax[i]-node2->pMin[i],node2->pMax[i]-node1->pMin[i]);
      max += dx*dx;
    }
  }

  // -------------------------------------------------------------------------- //

  // Counting pairs recursive function
  void KdTree::m_countPairsSerial(shared_ptr<Node> node1,
                                  shared_ptr<Node> node2,
                                  Catalog& cat1,
                                  Catalog& cat2,
                                  std::vector<double>& pairCounts,
                                  bool allPairs,
			          InputCatType catType1, 
			          InputCatType catType2)
  {
    // test label level to reject distant nodes when counting same cat //
    if ((node1->locCode < node2->locCode) &&
        (node1->hasChildren == false)  &&
        (node2->hasChildren == false)  &&
        (allPairs == false))  {
      return;
    }

    // Test node minimum and maximum node-node distances
    double dMin, dMax;
    m_distNodes(node1,node2,dMin,dMax);

    // case 2D Cartesian
    if (statIs2DCART()) {
      if ( ((0.25*dMin > m_scales.max1Dsq) || (dMax < m_scales.min1Dsq)) &&
           ((0.25*dMin > m_scales.max2Dsq) || (dMax < m_scales.min2Dsq))  ) {
        return;
      }
    } else {
      // cases: 1D or 2D Polar
      if ( (dMax < m_scales.min1Dsq) || (dMin > m_scales.max1Dsq) ) {
        return;
      }
    }

    // if both leaves then count pairs
    if ((node1->hasChildren == false) && (node2->hasChildren == false)) {
      double useWeight = (true == m_useWeight) ? 1.0 : 0.0;
      
      // loop over all the elements in the current nodes
      for (size_t i=node1->first; i<node1->last; ++i) {
        Galaxy& gal1 = cat1[i];
        for (size_t j=node2->first; j<node2->last; ++j) {
          // skip equal objects in case of same catalog counting
          if ((allPairs==false) && (node1->locCode == node2->locCode) && (i <= j)) continue;
          Galaxy& gal2 = cat2[j];
	  TwoPointCorrelation::fillPairCounts(gal1, gal2, pairCounts, useWeight);
        }
      }
    } else {
      // delegate counting to sub-nodes
      if (node2->hasChildren == false) {
        m_countPairsSerial(m_lookupNode((node1->locCode<<1)|0,catType1), node2, cat1, cat2, pairCounts, allPairs, catType1, catType2);
        m_countPairsSerial(m_lookupNode((node1->locCode<<1)|1,catType1), node2, cat1, cat2, pairCounts, allPairs, catType1, catType2);
      } else {
        m_countPairsSerial(node1, m_lookupNode((node2->locCode<<1)|0,catType2), cat1, cat2, pairCounts, allPairs, catType1, catType2);
        m_countPairsSerial(node1, m_lookupNode((node2->locCode<<1)|1,catType2), cat1, cat2, pairCounts, allPairs, catType1, catType2);
      }
    }
  }

  } /* end namespace LE3_GC_2PCF */
} /* end namespace le3gc */
