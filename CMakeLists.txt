CMAKE_MINIMUM_REQUIRED(VERSION 2.8.5)


#===============================================================================
# Load macros and functions for Elements-based projects
#===============================================================================

find_package(ElementsProject)
option(ELEMENTS_PARALLEL "" ON)


#===============================================================================
# Declare project name and version
# Example with dependency:
#                         elements_project(MyProject 1.0 USE Element 3.9)
#===============================================================================

elements_project(LE3_GC_TwoPointCorrelation 4.1	USE Elements 6.3.0
                                                    LE3_GC_CommonLibs 4.3
                                                    ST_DataModel 10.0.3)
                                                    
