# Galaxy Clustering Two Point Correlation

The project **LE3_GC_TwoPointCorrelation** is the Euclid Processing Function used 
to measure the two-point statistics in configuration space for galaxy redshift catalogs.

All the details on the code configuration options and performances are available 
in the **Software User Manual**.


## Information on Code Development

+ Version:
   + **4.0**
+ Environment
   + **EDEN 3.1**
+ Dependencies:
   + **LE3_GC_CommonLibs 4.1**
   + **Elements 6.2.1**


## How to get the code

#### Euclis SGS installation 
Within the LODEEN virtual machine, the code is dispatched through the *cvmfs* and it 
is available with its latest stable version.  
The *LODEEN* virtual machine that provides all the needed packages to run and 
develop Euclid software and can be found [here](https://euclid.roe.ac.uk/projects/codeen-users/wiki/User_Lodeen-installUpgrade).

#### Local installation
For a local copy, the project code can be downloaded from the Euclid GitLab [repository](https://gitlab.euclid-sgs.uk/PF-LE3-GC/LE3_GC_TwoPointCorrelation).  

* **<PERSON><PERSON>** via SSH the project in your *$HOME/Work/Projects* folder
  ```bash
  $ cd $HOME/Work/Projects
  $ <NAME_EMAIL>:PF-LE3-GC/LE3_GC_TwoPointCorrelation.git
  ```

* **Build** and **install** the *LE3_GC_TwoPointCorrelation* package within a [EDEN-like environment](https://euclid.roe.ac.uk/projects/codeen-users/wiki/EDEN)  
  ```bash
  $ cd $HOME/Work/Projects/LE3_GC_TwoPointCorrelation
  $ make
  $ make install
  ```

* **Verify** the code functionality with a basic set of checks
  ```bash
  $ cd $HOME/Work/Projects/LE3_GC_TwoPointCorrelation
  $ make test
  ```
  
  ## How to use the code
The code can be used in two ways:
* [**LOCAL**](#local-mode-run) mode, outside the EC infrastructure, by using and producing FITS files only
* [**PIPELINE**](#pipeline-mode-run) mode, within the EC infrastructure and relying on the [********************](https://gitlab.euclid-sgs.uk/PF-LE3-GC/********************) project, by using and producing Euclid XML products


### Local mode run
After the compilation and istallation, the code can be directly used with FITS files
I/O objects with the command:

```bash
$ E-Run LE3_GC_TwoPointCorrelation LE3_GC_ComputeTwoPointCorrelation [options]
```

The **options**, listed with *--help*, are:

```bash
--workdir, the working directory absolute path [Default: the project directory]
--parfile,  that configuration (.ini) file
--input_sky, the FITS file name containing the galaxy catalog
--input_ref,  the FITS file name containing the random catalog
--input_rec,  the FITS file name containing the reconstructed random catalog
--input_sky2, the FITS file name containing the galaxy catalog for cross estimator
--input_ref2,  the FITS file name containing the random catalog for cross estimator
--input_rec2,  the FITS file name containing the reconstructed random catalog for cross estimator
--input_pair, the FITS file name containing the galaxy pairs
--logdir, the optional directory where to save logs
--euclid_results, the JSON file name containig the list of FITS produced [Default: 2PCF_results.json]
```
**All (file) paths are relative to workdir**

Specific choices for the two-point correlation setup can be made in configuration file.  
A template for che configuration is available in [**LE3_GC_2PCF/conf/parameters_definition.ini**](https://gitlab.euclid-sgs.uk/PF-LE3-GC/LE3_GC_TwoPointCorrelation/-/blob/develop/LE3_GC_2PCF/conf/parameters_definition.ini?ref_type=heads).

> **NOTE**
> The code will always write the output files in the *<workdir>/data/* folder.  
> If no <workdir> is specified, the results will be written within the project folder *LE3_GC_TwoPointCorrelation/data/*.  
> This behavior is required for the Euclid SGS pipelin(s) structure and cannot be changed.


```mermaid
flowchart TB
   linkStyle default interpolate basis

   subgraph Local Mode Run - LE3_GC_2PCF module
      direction TB

      subgraph null[" "]
         direction LR
         A(E-Run   LE3_GC_TwoPointCorrelation   LE3_GC_ComputeTwoPointCorrelation)
         style A1 text-align:left
         A --- A1
         A1["--workdir   = < absolute path >
            --parfile    = < relative path to .ini configuration file >
            --input_sky  = < data catalog FITS file relative path >
            --input_ref  = < random catalog FITS file relative path >
            --input_rec  = < the FITS file name containing the reconstructed random catalog >
            --input_sky2 = < the FITS file name containing the galaxy catalog for cross estimator >
            --input_ref2 = < the FITS file name containing the random catalog for cross estimator >
            --input_rec2 = < the FITS file name containing the reconstructed random catalog for cross estimator >
            --input_pair = < the FITS file name containing the galaxy pairs >"]
      end
      null --> null2

      subgraph null2["Processing "]
         direction TB
         B[Configure 2PCF with parameter options]
         B --> C
         C[Count Pairs]
         C --> D
         D[Compute Correlation]
         D --> E
         E[Write result FITS files]
      end

      null2 --> null3

      subgraph null3[" "]
         direction TB
         F[/"two-point correlation FITS file(s)"/]
         G[/"JSON file listing two-point correlation FITS files produced"/]
      end
end
```

### Pipeline mode run

Whithin the EC SGS infrastructure the code should process Euclid products: XML 
files that reference datacontainers (FITS).  
The XML products are handled via two interface programs, part of the theLE3_GC_2PCF_IO module:

* **LE3_GC_2PCF_Input** to parse the XML file products to retrieve the FITS
file datacontainers to be *passed* to the *LE3_GC_ComputeTwoPointCorrelation* program.
* **LE3_GC_2PCF_Output** to collect the *LE3_GC_ComputeTwoPointCorrelation* results and 
produce the output XML product for the EC infratructure

To run the code in the EC infrastructure, the *LE3_GC_ComputeTwoPointCorrelation* program 
should be part of an IAL pipeline that calls, in the order:
1. the LE3_GC_2PCF_Input code of theLE3_GC_2PCF_IO module
2. the LE3_GC_ComputeTwoPointCorrelation in the LE3_GC_2PCF module
3. the LE3_GC_2PCF_Output code of theLE3_GC_2PCF_IO module


#### LE3_GC_2PCF_Input
The program is an *EuclidWrapper* to handle and forward the input products to the
*LE3_GC_ComputeTwoPointCorrelation* code.

The program is called with the command:

```bash
$ ERun LE3_GC_TwoPointCorrelation EuclidWrapper --wrapper-module LE3_GC_2PCF_IO.LE3_GC_2PCF_Input  LE3_GC_ComputeTwoPointCorrelation --forward-ial-api [options]
```

The **options** are the same of the [LE3_GC_ComputeTwoPointCorrelation](#local-mode-run) executable. The following are going to be manipulated by the wrapper:

```bash
--parfile,  the configuration XML product
--input_sky, the XML product containing the galaxy catalog
--input_ref,  the XML product containing the random catalog and the dndz/redshift_bin_info
--input_rec,  the FITS file name containing the reconstructed random catalog
--input_sky2, the FITS file name containing the galaxy catalog for cross estimator
--input_ref2,  the FITS file name containing the random catalog for cross estimator
--input_rec2,  the FITS file name containing the reconstructed random catalog for cross estimator
--input_pair, the FITS file name containing the galaxy pairs
```

**All (file) paths are relative to workdir**

The input **XML products** are parsed to get the name of the datacontainers referenced 
(*FITS* files for catalogs, *ini* file for configuration). The names of the 
datacontainers are forwarded to the *LE3_GC_ComputeTwoPointCorrelation* program input ports.

>Note: The configuration XML product references 2 datacontainers: one with the parameters
> for the cosmology, and one with the 2PCF-GC specific configuration parameters.  
> The two files are joined together into a *parameter.ini* file forwarded to
> the *LE3_GC_ComputeTwoPointCorrelation*. This is done to have a configuration ".ini" file 
> with the same structure expected from the *LE3_GC_ComputeTwoPointCorrelation* program


####  LE3_GC_2PCF_Output
The program uses the results produced by the *LE3_GC_ComputeTwoPointCorrelation* to 
generate the Euclid XML product for the LE3-GC-2PCF PF

The program is called with the command:
```bash
$ E-Run LE3_GC_TwoPointCorrelation LE3_GC_2PCF_Output [options]
```

The **options**, listed with *--help*, are:

```bash
--workdir, the working directory absolute path
--euclid_results, the JSON filename with the list of FITS files created
--output_file, the euclid XML product filename for the LE3-GC-2PCF
```

**All (file) paths are relative to workdir**

The overall flow for the pipeline mode run using the Input/Outpu wrapper modules is:

```mermaid
flowchart TB
   linkStyle default interpolate basis

   subgraph Pipeline Mode Run
      direction TB

      subgraph null["LE3_GC_2PCF_Input module"]
         direction LR
         A(ERun LE3_GC_TwoPointCorrelation EuclidWrapper --wrapper-module LE3_GC_2PCF_IO.LE3_GC_2PCF_Input  LE3_GC_ComputeTwoPointCorrelation --forward-ial-api)
         style A1 text-align:left
         A --- A1
         A1["--workdir  = < absolute path >
            --parfile   = < relative path to XML configuration product >
            --input_sky = < data catalog XML product relative path >
            --input_ref = < random catalog XML product relative path >
            --input_rec  = < the FITS file name containing the reconstructed random catalog >
            --input_sky2 = < the FITS file name containing the galaxy catalog for cross estimator >
            --input_ref2 = < the FITS file name containing the random catalog for cross estimator >
            --input_rec2 = < the FITS file name containing the reconstructed random catalog for cross estimator >
            --input_pair = < the FITS file name containing the galaxy pairs >"]
      end
      null --> null2

      subgraph null2[" "]
         direction TB
         P1[/"parfile [.ini]"/]
         P2[/"input_sky [FITS]"/]
         P3[/"input_ref [FITS]"/]
         P4[/"input_nbar [FITS]"/]
      end
      null2 -- forward input dataset --> null3

      subgraph null3["LE3_GC_2PCF module"]
         direction LR
         B(E-Run   LE3_GC_TwoPointCorrelation   LE3_GC_ComputeTwoPointCorrelation)
         style B1 text-align:left
         B --- B1
         B1["--workdir  = < absolute path >
            --parfile   = < relative path to .ini configuration file >
            --input_sky = < data catalog FITS file relative path >
            --input_ref = < random catalog FITS file relative path >
            --input_rec  = < the FITS file name containing the reconstructed random catalog >
            --input_sky2 = < the FITS file name containing the galaxy catalog for cross estimator >
            --input_ref2 = < the FITS file name containing the random catalog for cross estimator >
            --input_rec2 = < the FITS file name containing the reconstructed random catalog for cross estimator >
            --input_pair = < the FITS file name containing the galaxy pairs >"]
      end

      null3 --> null4

      subgraph null4[" "]
         direction TB
         H[/"two-point correlation FITS file(s)"/]
         I[/"JSON file listing Two-Point Correlation FITS files produced"/]
      end

      null4 -- forward output files --> null5

      subgraph null5["LE3_GC_2PCF_Output module"]
         direction LR
         C(ERun LE3_GC_TwoPointCorrelation LE3_GC_2PCF_Output)
         style C1 text-align:left
         C --- C1
         C1["--workdir       = < absolute path >
            --euclid_results = < relative path to JSON file listing Two-Point Correlation FITS files produced >
            --output_file    = < euclid output XML product relative path >"]
      end

      null5 --> R1
      R1[/"Euclid 2PCF-GC XML output"/]
end
```

## Example Runs
In order to verify that the code runs without any problems, it is possible to execute it using some testing dataset provided with the installation. This check is available both in pipeline and local mode.
The easiest way to do this, is by invoking the following command
```bash
$ E-Run LE3_GC_TwoPointCorrelation LE3_GC_ComputeTwoPointCorrelation --workdir /u/user/Work/Projects/LE3_GC_TwoPointCorrelation/LE3_GC_2PCF/tests/examples --parfile data/parameters_fits.ini --input_sky data/example_sky.fits --input_ref data/example_ref.fits --euclid_results out.json
```
which uses as input dataset the sky and reference catalogs *example_sky* and *example_ref* under /LE3_GC_2PCF/tests/examples/data.
Similarly, it is possible to perform a check of the pipeline mode which requires XML files as input datasets. In this case the command is
```bash
$ E-Run LE3_GC_TwoPointCorrelation EuclidWrapper --wrapper-module LE3_GC_2PCF_IO.LE3_GC_2PCF_Input LE3_GC_ComputeTwoPointCorrelation --forward-ial-api --workdir ~/Work/Projects/LE3_GC_TwoPointCorrelation/LE3_GC_2PCF/tests/examples/ --input_sky sky.xml --input_ref ref.xml --parfile conf.xml --euclid_results out.json
```


## Copyright
Copyright (C) 2012-2020 Euclid Science Ground Segment - LGPL3.

This library is free software; you can redistribute it and/or modify it under
the terms of the GNU Lesser General Public License as published by the Free
Software Foundation; either version 3.0 of the License, or (at your option)
any later version.

This library is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
details.
