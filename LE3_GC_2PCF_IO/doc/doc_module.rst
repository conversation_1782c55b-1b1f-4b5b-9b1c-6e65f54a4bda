Module Overview <----------------- TO BE WRITTEN ------------------------>
==========================================================================

.. important:: Please edit ./@_el_pack_short@/doc/module.rst to replace this section.

.. The following sections can be replaced or updated. 
   You may also use a toctree directive for sub documents. 

Introduction
------------

Purpose of this Elements Module.

What does it do?

Architecture overview
---------------------

Highlevel overview of the Module.

How is it done?

  1. List of packages
  2. Explanation of used design patterns
  3. Useful UML diagrams: UML Package, UML Structure and Deployment, UML Use Case