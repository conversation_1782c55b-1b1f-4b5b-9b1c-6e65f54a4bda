 #
# Copyright (C) 2012-2020 Euclid Science Ground Segment
#
# This library is free software; you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation; either version 3.0 of the License, or (at your option)
# any later version.
#
# This library is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this library; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
#

"""
:file: python/LE3_GC_2PCF_IO/LE3_GC_2PCF_IO_CreateProduct.py

:date: 04/12/22
:author: F. RIZZO

"""

import os
import lxml.builder
from   astropy.io import fits

import ElementsKernel.Logging as log

from LE3_GC_Libraries.LE3_ManageDpdBase import LE3_ManageDpdBase
import LE3_GC_TWOPOINTCORRELATION_VERSION

class LE3_GC_2PCF_IO_CreateProduct(LE3_ManageDpdBase):

    FILE_VERSION = '0.3'


    def __init__(self):
        """summary """
        super().__init__()
        # replace defaults
        self._logger = log.getLogger(__class__.__name__)
        self._env_info['proj_name'] = os.environ['THIS_PROJECT_ROOT'][os.environ['THIS_PROJECT_ROOT'].rfind('/')+1:]
        self._env_info['proj_vers'] = LE3_GC_TWOPOINTCORRELATION_VERSION.LE3_GC_TWOPOINTCORRELATION_ORIGINAL_VERSION
    # ------------------------------------------------------------------------ #


    def _get_info_from_fits(self, fits_name:str, hdu_name:str, key_dict:dict) -> dict:
        """_summary_

        Args:
            fits_name (str): _description_
            hdu_name (str): _description_
            key_dict (dict): _description_

        Returns:
            dict: _description_
        """
        if fits_name is None :
            info = {key : '0' for key in key_dict}
        
        else :

          hdus = fits.open(fits_name, mode='readonly')
          info_table = hdus[hdu_name].header

          # get info dict for all KEYWORDS and set to None keywords not found in FITS
          info = {k:info_table.get(k, '') for k in key_dict.values()}
          hdus.close()

        return info
    # ------------------------------------------------------------------------ #


    def get_product_and_namespace(self,xsd_name:str) -> tuple:
        """_summary_

        Args:
            xsd_name (str): _description_

        Returns:
            tuple: (produce_name, namespace)
        """
        # path to  datamodel files
        dm_root_path = os.environ['ST_DATAMODEL_PROJECT_ROOT']
        gc_rel_path = f"/InstallArea/{os.environ['BINARY_TAG'][:-3]}o2g/auxdir/ST_DM_Schema/dpd/le3/gc"

        # get full pqath to xsd file
        xsd = '/'.join([dm_root_path, gc_rel_path, xsd_name])

        # get xsd root element
        root = lxml.etree.parse(xsd).getroot()

        namespace = root.attrib['targetNamespace']
        product = [e for e in root.getchildren() if 'element' in str(e.tag)][0].attrib['name']

        return product, namespace
    # ------------------------------------------------------------------------ #


    def _prepare_auto_1D(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS  = {'DataCatalog'     : 'D_NAME',
                       'RandCatalog'     : 'R_NAME',
                       'RunType'         : 'RUNTYPE',
                       'SelectionID'     : 'SELECT',
                       'Statistics'      : 'STAT',
                       'CountingMethod'  : 'METHOD',
                       'BinType'         : 'BIN_TYPE',
                       'BinNumber'       : 'BIN_NUM',
                       'BinMinValue'     : 'BIN_MIN',
                       'BinMaxValue'     : 'BIN_MAX',
                       'UseWeight'       : 'WEIGHT',
                       'SplitFactor'     : 'SPLIT',
                       'CosmologyID'     : 'COSMO_ID',
                       'ReleaseName'     : 'SELECT',
                       'Blinding'        : 'BLINDING'
        }
        
        # get relevant datacontainers from JSON dict
        fits_file = filenames['AutoCorrelationFile']
        pair_file = filenames['AutoPairsFile']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-auto.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.pair.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.corr.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #


    def _prepare_auto_2Dcart(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog'     : 'D_NAME',
                      'RandCatalog'     : 'R_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID'     : 'SELECT',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinTypeDim1'     : 'BIN1TYPE',
                      'BinNumberDim1'   : 'BIN1NUM',
                      'BinMinValueDim1' : 'BIN1MIN',
                      'BinMaxValueDim1' : 'BIN1MAX',
                      'BinTypeDim2'     : 'BIN2TYPE',
                      'BinNumberDim2'   : 'BIN2NUM',
                      'BinMinValueDim2' : 'BIN2MIN',
                      'BinMaxValueDim2' : 'BIN2MAX',
                      'PiMax'           : 'PI_MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT',
                      'Blinding'        : 'BLINDING'
                    }

        fits_file_cart = filenames['AutoCorrelationFile2Dcart']
        fits_file_proj = filenames['AutoCorrelationFile2Dproj']
        pair_file = filenames['AutoPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-auto-cart.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_cart, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_cart)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileProjected(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_proj)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.corr.proj",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #

    def _prepare_auto_2Dpol(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog'     : 'D_NAME',
                      'RandCatalog'     : 'R_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID'     : 'SELECT',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinTypeDim1'     : 'BIN1TYPE',
                      'BinNumberDim1'   : 'BIN1NUM',
                      'BinMinValueDim1' : 'BIN1MIN',
                      'BinMaxValueDim1' : 'BIN1MAX',
                      'BinTypeDim2'     : 'BIN2TYPE',
                      'BinNumberDim2'   : 'BIN2NUM',
                      'BinMinValueDim2' : 'BIN2MIN',
                      'BinMaxValueDim2' : 'BIN2MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT',
                      'LosDef'          : 'LOS_DEF',
                      'Blinding'        : 'BLINDING'
        }

        fits_file_polar = filenames['AutoCorrelationFile2Dpolar']
        fits_file_multi = filenames['AutoCorrelationFile2Dmulti']
        pair_file = filenames['AutoPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-auto-pol.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_multi, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_polar)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileMultipole(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_multi)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.corr.mul",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #
    # ------------------------------------------------------------------------ #


    def _prepare_cross_1D(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS  = {'DataCatalog1'     : 'D1_NAME',
                        'RandCatalog1'    : 'R1_NAME',
                        'DataCatalog2'    : 'D2_NAME',
                        'RandCatalog2'    : 'R2_NAME',
                        'RunType'         : 'RUNTYPE',
                        'SelectionID1'    : 'SELECT1',
                        'SelectionID2'    : 'SELECT2',
                        'Statistics'      : 'STAT',
                        'CountingMethod'  : 'METHOD',
                        'BinType'         : 'BIN_TYPE',
                        'BinNumber'       : 'BIN_NUM',
                        'BinMinValue'     : 'BIN_MIN',
                        'BinMaxValue'     : 'BIN_MAX',
                        'UseWeight'       : 'WEIGHT',
                        'SplitFactor'     : 'SPLIT',
                        'CosmologyID'     : 'COSMO_ID',
                        'ReleaseName'     : 'SELECT1',
                        'Blinding'        : 'BLINDING'
        }

        fits_file = filenames['CrossCorrelationFile']
        pair_file = filenames['CrossPairsFile']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-cross.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.pair.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.corr.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #


    def _prepare_cross_2Dcart(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS  = {'DataCatalog1'    : 'D1_NAME',
                       'RandCatalog1'    : 'R1_NAME',
                       'DataCatalog2'    : 'D2_NAME',
                       'RandCatalog2'    : 'R2_NAME',
                       'RunType'         : 'RUNTYPE',
                       'SelectionID1'    : 'SELECT1',
                       'SelectionID2'    : 'SELECT2',
                       'Statistics'      : 'STAT',
                       'CountingMethod'  : 'METHOD',
                       'BinTypeDim1'     : 'BIN1TYPE',
                       'BinNumberDim1'   : 'BIN1NUM',
                       'BinMinValueDim1' : 'BIN1MIN',
                       'BinMaxValueDim1' : 'BIN1MAX',
                       'BinTypeDim2'     : 'BIN2TYPE',
                       'BinNumberDim2'   : 'BIN2NUM',
                       'BinMinValueDim2' : 'BIN2MIN',
                       'BinMaxValueDim2' : 'BIN2MAX',
                       'PiMax'           : 'PI_MAX',
                       'UseWeight'       : 'WEIGHT',
                       'SplitFactor'     : 'SPLIT',
                       'CosmologyID'     : 'COSMO_ID',
                       'ReleaseName'     : 'SELECT1',
                       'Blinding'        : 'BLINDING'
        }           
        fits_file_cart = filenames['CrossCorrelationFile2Dcart']
        fits_file_proj = filenames['CrossCorrelationFile2Dproj']
        pair_file = filenames['CrossPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-cross-cart.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_cart, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_cart)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileProjected(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_proj)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.corr.proj",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #

    def _prepare_cross_2Dpol(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog1'    : 'D1_NAME',
                      'RandCatalog1'    : 'R1_NAME',
                      'DataCatalog2'    : 'D2_NAME',
                      'RandCatalog2'    : 'R2_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID1'     : 'SELECT1',
                      'SelectionID2'     : 'SELECT2',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinTypeDim1'     : 'BIN1TYPE',
                      'BinNumberDim1'   : 'BIN1NUM',
                      'BinMinValueDim1' : 'BIN1MIN',
                      'BinMaxValueDim1' : 'BIN1MAX',
                      'BinTypeDim2'     : 'BIN2TYPE',
                      'BinNumberDim2'   : 'BIN2NUM',
                      'BinMinValueDim2' : 'BIN2MIN',
                      'BinMaxValueDim2' : 'BIN2MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT1',
                      'LosDef'          : 'LOS_DEF',
                      'Blinding'        : 'BLINDING'
        }

        fits_file_polar = filenames['CrossCorrelationFile2Dpolar']
        fits_file_multi = filenames['CrossCorrelationFile2Dmulti']
        pair_file = filenames['CrossPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-cross-pol.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_multi, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_polar)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileMultipole(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_multi)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.corr.mul",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #
    # ------------------------------------------------------------------------ #


    def _prepare_recon_1D(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog'     : 'D_NAME',
                      'RandCatalogR'    : 'R_NAME',
                      'RandCatalogS'    : 'R_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID'     : 'SELECT',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinType'         : 'BIN_TYPE',
                      'BinNumber'       : 'BIN_NUM',
                      'BinMinValue'     : 'BIN_MIN',
                      'BinMaxValue'     : 'BIN_MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'ReconstructionID': 'SELECT',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT',
                      'Blinding'        : 'BLINDING'
        }

        fits_file = filenames['ReconCorrelationFile']
        pair_file = filenames['ReconPairsFile']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-rec-auto.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.pair.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.corr.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #

    def _prepare_recon_2Dcart(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog'     : 'D_NAME',
                      'RandCatalogR'    : 'R_NAME',
                      'RandCatalogS'    : 'R_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID'     : 'SELECT',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinTypeDim1'     : 'BIN1TYPE',
                      'BinNumberDim1'   : 'BIN1NUM',
                      'BinMinValueDim1' : 'BIN1MIN',
                      'BinMaxValueDim1' : 'BIN1MAX',
                      'BinTypeDim2'     : 'BIN2TYPE',
                      'BinNumberDim2'   : 'BIN2NUM',
                      'BinMinValueDim2' : 'BIN2MIN',
                      'BinMaxValueDim2' : 'BIN2MAX',
                      'PiMax'           : 'PI_MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'ReconstructionID': 'SELECT',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT',
                      'Blinding'        : 'BLINDING'
        }
        fits_file_cart = filenames['ReconCorrelationFile2Dcart']
        fits_file_proj = filenames['ReconCorrelationFile2Dproj']
        pair_file = filenames['ReconPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-rec-auto-cart.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_cart, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_cart)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileProjected(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_proj)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.corr.proj",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #

    def _prepare_recon_2Dpol(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog'     : 'D_NAME',
                      'RandCatalogR'    : 'R_NAME',
                      'RandCatalogS'    : 'R_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID'     : 'SELECT',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinTypeDim1'     : 'BIN1TYPE',
                      'BinNumberDim1'   : 'BIN1NUM',
                      'BinMinValueDim1' : 'BIN1MIN',
                      'BinMaxValueDim1' : 'BIN1MAX',
                      'BinTypeDim2'     : 'BIN2TYPE',
                      'BinNumberDim2'   : 'BIN2NUM',
                      'BinMinValueDim2' : 'BIN2MIN',
                      'BinMaxValueDim2' : 'BIN2MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'ReconstructionID': 'SELECT',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT',
                      'LosDef'          : 'LOS_DEF',
                      'Blinding'        : 'BLINDING'
        }

        fits_file_polar = filenames['ReconCorrelationFile2Dpolar']
        fits_file_multi = filenames['ReconCorrelationFile2Dmulti']
        pair_file = filenames['ReconPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-rec-auto-pol.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_multi, 'CORRELATION', XML_2_FITS)
        
        # bug in DM CorrelationFileProjected must be changed to CorrelationFileMulti
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_polar)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileProjected(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_multi)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.auto.rec.corr.mul",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #
    # ------------------------------------------------------------------------ #


    def _prepare_cross_recon_1D(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog1'    : 'D1_NAME',
                      'RandCatalogR1'    : 'R1_NAME',
                      'RandCatalogS1'    : 'R1_NAME',
                      'DataCatalog2'    : 'D2_NAME',
                      'RandCatalogR2'    : 'R2_NAME',
                      'RandCatalogS2'    : 'R2_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID1'    : 'SELECT1',
                      'SelectionID2'    : 'SELECT2',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinType'         : 'BIN_TYPE',
                      'BinNumber'       : 'BIN_NUM',
                      'BinMinValue'     : 'BIN_MIN',
                      'BinMaxValue'     : 'BIN_MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'ReconstructionID': 'SELECT1',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT1',
                      'Blinding'        : 'BLINDING'
        }

        fits_file = filenames['CrossReconCorrelationFile']
        pair_file = filenames['CrossReconPairsFile']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-rec-cross.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.pair.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.corr.1D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #

    def _prepare_cross_recon_2Dcart(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS = {'DataCatalog1'    : 'D1_NAME',
                      'RandCatalogR1'   : 'R1_NAME',
                      'RandCatalogS1'   : 'R1_NAME',
                      'DataCatalog2'    : 'D2_NAME',
                      'RandCatalogR2'   : 'D2_NAME',
                      'RandCatalogS2'   : 'R2_NAME',
                      'RunType'         : 'RUNTYPE',
                      'SelectionID1'    : 'SELECT1',
                      'SelectionID2'    : 'SELECT2',
                      'Statistics'      : 'STAT',
                      'CountingMethod'  : 'METHOD',
                      'BinTypeDim1'     : 'BIN1TYPE',
                      'BinNumberDim1'   : 'BIN1NUM',
                      'BinMinValueDim1' : 'BIN1MIN',
                      'BinMaxValueDim1' : 'BIN1MAX',
                      'BinTypeDim2'     : 'BIN2TYPE',
                      'BinNumberDim2'   : 'BIN2NUM',
                      'BinMinValueDim2' : 'BIN2MIN',
                      'BinMaxValueDim2' : 'BIN2MAX',
                      'PiMax'           : 'PI_MAX',
                      'UseWeight'       : 'WEIGHT',
                      'SplitFactor'     : 'SPLIT',
                      'ReconstructionID': 'SELECT1',
                      'CosmologyID'     : 'COSMO_ID',
                      'ReleaseName'     : 'SELECT1',
                      'Blinding'        : 'BLINDING'
        }
        fits_file_cart = filenames['CrossReconCorrelationFile2Dcart']
        fits_file_proj = filenames['CrossReconCorrelationFile2Dproj']
        pair_file = filenames['CrossReconPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-rec-cross-cart.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_cart, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_cart)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileProjected(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_proj)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.corr.proj",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #
    
    def _prepare_cross_recon_2Dpol(self, filenames:dict)-> lxml.etree.Element:
        XML_2_FITS  = {'DataCatalog1'    : 'D1_NAME',
                       'RandCatalogR1'   : 'R1_NAME',
                       'RandCatalogS1'   : 'R1_NAME',
                       'DataCatalog2'    : 'D2_NAME',
                       'RandCatalogR2'   : 'R2_NAME',
                       'RandCatalogS2'   : 'R2_NAME',
                       'RunType'         : 'RUNTYPE',
                       'SelectionID1'    : 'SELECT1',
                       'SelectionID2'    : 'SELECT2',
                       'Statistics'      : 'STAT',
                       'CountingMethod'  : 'METHOD',
                       'BinTypeDim1'     : 'BIN1TYPE',
                       'BinNumberDim1'   : 'BIN1NUM',
                       'BinMinValueDim1' : 'BIN1MIN',
                       'BinMaxValueDim1' : 'BIN1MAX',
                       'BinTypeDim2'     : 'BIN2TYPE',
                       'BinNumberDim2'   : 'BIN2NUM',
                       'BinMinValueDim2' : 'BIN2MIN',
                       'BinMaxValueDim2' : 'BIN2MAX',
                       'UseWeight'       : 'WEIGHT',
                       'SplitFactor'     : 'SPLIT',
                       'ReconstructionID': 'SELECT1',
                       'CosmologyID'     : 'COSMO_ID',
                       'ReleaseName'     : 'SELECT1',
                       'LosDef'          : 'LOS_DEF',
                       'Blinding'        : 'BLINDING'
         }
    
        fits_file_polar = filenames['CrossReconCorrelationFile2Dpolar']
        fits_file_multi = filenames['CrossReconCorrelationFile2Dmulti']
        pair_file = filenames['CrossReconPairsFile2D']
        
        # get XML header ad update with current PF name/version
        self._set_header("le3/gc/euc-le3-gc-2pcf-rec-cross-pol.xsd")
        
        # Read info from correlation FITS header
        info = self._get_info_from_fits(fits_file_multi, 'CORRELATION', XML_2_FITS)
        
        tag = self._tag
        data = tag.Data(*[tag(xml_key, f'{info[fits_key]}') for xml_key, fits_key in XML_2_FITS.items() if xml_key != 'Blinding'],
                        tag.PairsFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(pair_file)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.pair.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFile(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_polar)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.corr.2D",
                            version=__class__.FILE_VERSION
                        ),
                        tag.CorrelationFileMultipole(
                            tag.DataContainer(
                                tag.FileName(os.path.basename(fits_file_multi)),
                                filestatus="PROPOSED"
                            ),
                            format="le3.gc.2pcf.cross.rec.corr.mul",
                            version=__class__.FILE_VERSION
                        ),
                        tag.Blinding(info['BLINDING'])
                 )
        self._set_data(data)
    # ------------------------------------------------------------------------ #
    # ------------------------------------------------------------------------ #


    def prepare_product(self, filenames:dict):
    
        # AUTO estimator
        tree = ''
        if 'AutoCorrelationFile' in filenames and \
           'AutoPairsFile' in filenames:
            self._prepare_auto_1D(filenames)
        elif 'AutoCorrelationFile2Dcart' in filenames and \
             'AutoCorrelationFile2Dproj' in filenames and \
             'AutoPairsFile2D' in filenames:
            self._prepare_auto_2Dcart(filenames)
        elif 'AutoCorrelationFile2Dpolar' in filenames and \
             'AutoCorrelationFile2Dmulti' in filenames and \
             'AutoPairsFile2D' in filenames:
            self._prepare_auto_2Dpol(filenames)

        # CROSS estimator
        elif 'CrossCorrelationFile' in filenames and \
             'CrossPairsFile' in filenames:
            self._prepare_cross_1D(filenames)
        elif 'CrossCorrelationFile2Dcart' in filenames and \
             'CrossCorrelationFile2Dproj' in filenames and \
             'CrossPairsFile2D' in filenames:
            self._prepare_cross_2Dcart(filenames)
        elif 'CrossCorrelationFile2Dpolar' in filenames and \
             'CrossCorrelationFile2Dmulti' in filenames and \
             'CrossPairsFile2D' in filenames:
            self._prepare_cross_2Dpol(filenames)
              
        # RECONSTRUCTED estimator
        elif 'ReconCorrelationFile' in filenames and \
             'ReconPairsFile' in filenames:
            self._prepare_recon_1D(filenames)
        elif 'ReconCorrelationFile2Dcart' in filenames and \
             'ReconCorrelationFile2Dproj' in filenames and \
             'ReconPairsFile2D' in filenames:
            self._prepare_recon_2Dcart(filenames)
        elif 'ReconCorrelationFile2Dpolar' in filenames and \
             'ReconCorrelationFile2Dmulti' in filenames and \
             'ReconPairsFile2D' in filenames:
            self._prepare_recon_2Dpol(filenames)

        # CROSS RECONSTRUCTED estimator
        elif 'CrossReconCorrelationFile' in filenames and \
             'CrossReconPairsFile' in filenames:
            self._prepare_cross_recon_1D(filenames)
        elif 'CrossReconCorrelationFile2Dcart' in filenames and \
             'CrossReconCorrelationFile2Dproj' in filenames and \
             'CrossReconPairsFile2D' in filenames:
            self._prepare_cross_recon_2Dcart(filenames)
        elif 'CrossReconCorrelationFile2Dpolar' in filenames and \
             'CrossReconCorrelationFile2Dmulti' in filenames and \
             'CrossReconPairsFile2D' in filenames:
            self._prepare_cross_recon_2Dpol(filenames)
        else:
            print("I'm sorry Mario, your princess is in another castle!")
            quit()
    # ------------------------------------------------------------------------ #


    def write_xml(self, name:str, product_id:str=None):
        super().write(name, product_id)
    # ------------------------------------------------------------------------ #
