#
#  Copyright (C) 2012-2020 Euclid Science Ground Segment
# 
#  This library is free software; you can redistribute it and/or modify it under
#  the terms of the GNU Lesser General Public License as published by the Free
#  Software Foundation; either version 3.0 of the License, or (at your option)
#  any later version.
# 
#  This library is distributed in the hope that it will be useful, but WITHOUT
#  ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
#  FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
#  details.
# 
#  You should have received a copy of the GNU Lesser General Public License
#  along with this library; if not, write to the Free Software Foundation, Inc.,
#  51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
#
"""
:file:      LE3_GC_2PCF_Input.py
:date:      10/11/24
:author:  <PERSON><PERSON> <tavagna<PERSON>@inaf.it> [integration SDC-IT]
:author:  <PERSON> <<EMAIL>> [integration SDC-IT]
:copyright: (C) 2012-2024 Euclid Science Ground Segment - GNU General Public License
"""

import os
import argparse
import lxml.etree

from LE3_GC_TWOPOINTCORRELATION_INSTALL import LE3_GC_TWOPOINTCORRELATION_INSTALL_LOCATION as TWOPCF_PATH

CONFIGFILE = 'config_2PCF.ini'

def defineWrapperProgramOptions():
    parser = argparse.ArgumentParser()
    parser.add_argument('--parfile',    help='configuration XML file', required=True)
    parser.add_argument('--input_sky',  help='input data catalog XML product')
    parser.add_argument('--input_ref',  help='input random catalog XML product')
    parser.add_argument('--input_rec',  help='input reconstructed catalog XML product')
    parser.add_argument('--input_pair', help='input pairs 2PCF XML product')
    parser.add_argument('--input_sky2', help='input data catalog 2 XML product')
    parser.add_argument('--input_ref2', help='input random catalog 2 XML product')
    parser.add_argument('--input_rec2', help='input reconstructed catalog 2 XML produc')
    #parser.add_argument('--euclid_results', help='Output intermediate JSON product')
    return parser

def getOptionsToAppend(args, executable):
    """Intercept all wrapped module options to parse XMLs input forwarding datacontainers

    Args:
        args (dict): parsed parameters
        executable (_type_): wrapped executable module

    Returns:
        list: options to be forwarded to the wrapped module
    """
    forwarded_options = []

    #data_dir = os.path.join(args.workdir, 'data')
    data_dir = 'data'
    os.makedirs(data_dir, exist_ok=True)

    ### create single parameter file from configuration datacontainers
    euclid_catalog_defaults = os.path.join(TWOPCF_PATH, 'conf', 'euclid.ini')

    conf_root = lxml.etree.parse(args.parfile).getroot()
    conf_parfile = conf_root.findtext('./Data/ParameterFile/FileName')
    conf_cosmofile = conf_root.findtext('./Data/CosmologyFile/FileName')

    #create configuration object adding default falues and parameters from conf
    in_parfile = os.path.join(data_dir, CONFIGFILE)
    with open(in_parfile, 'w') as out_configfile:
        for tempfile in [euclid_catalog_defaults, 
                         os.path.join(data_dir, conf_parfile), 
                         os.path.join(data_dir, conf_cosmofile)]:
            with open(tempfile, 'r') as ofile:
                out_configfile.write(ofile.read())
    forwarded_options.extend(['--parfile', in_parfile])

    # read and parse xml file containing the fits sky catalog
    if args.input_sky:
        root_sky = lxml.etree.parse(args.input_sky).getroot()
        in_sky_cat = os.path.join('data', root_sky.findtext('./Data/Catalog/DataContainer/FileName'))
        forwarded_options.extend(['--input_sky', in_sky_cat])
    # read and parse xml file containing the fits reference catalog
    if args.input_ref:
        root_ref = lxml.etree.parse(args.input_ref).getroot()
        in_ref_cat = os.path.join('data', root_ref.findtext('./Data/Catalog/DataContainer/FileName'))
        forwarded_options.extend(['--input_ref', in_ref_cat])
    # read and parse xml file containing the fits reconstructed catalog
    if args.input_rec:
        root_rec = lxml.etree.parse(args.input_rec).getroot()
        in_rec_cat = os.path.join('data', root_rec.findtext('./Data/Catalog/DataContainer/FileName'))
        forwarded_options.extend(['--input_rec', in_rec_cat])
    # get pairs file name fom twopoint product
    if (args.input_pair):
        root_pairs = lxml.etree.parse(args.input_pair).getroot()
        in_pairs = os.path.join('data', root_pairs.findtext('./Data/PairsFile/DataContainer/FileName'))
        forwarded_options.extend(['--input_pair', in_pairs])

    # read and parse xml file containing the fits sky catalog 2
    if args.input_sky2:
        root_sky2 = lxml.etree.parse(args.input_sky2).getroot()
        in_sky_cat_2 = os.path.join('data', root_sky2.findtext('./Data/Catalog/DataContainer/FileName'))
        forwarded_options.extend(['--input_sky2', in_sky_cat_2])
    # read and parse xml file containing the fits reference catalog 2
    if args.input_ref2:
        root_ref2 = lxml.etree.parse(args.input_ref2).getroot() 
        in_ref_cat_2 = os.path.join('data', root_ref2.findtext('./Data/Catalog/DataContainer/FileName'))
        forwarded_options.extend(['--input_ref2', in_ref_cat_2])
    # read and parse xml file containing the fits reconstructed catalog 2
    if args.input_rec2:
        root_rec_2 = lxml.etree.parse(args.input_rec2).getroot()
        in_rec_cat_2 = os.path.join('data', root_rec_2.findtext('./Data/Catalog/DataContainer/FileName'))
        forwarded_options.extend(['--input_rec2', in_rec_cat_2])

    return forwarded_options
