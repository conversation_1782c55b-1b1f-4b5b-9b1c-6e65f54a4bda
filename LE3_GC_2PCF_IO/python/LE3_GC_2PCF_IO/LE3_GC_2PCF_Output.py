#
# Copyright (C) 2012-2020 Euclid Science Ground Segment
#
# This library is free software; you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation; either version 3.0 of the License, or (at your option)
# any later version.
#
# This library is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this library; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
#


"""
:file: python/LE3_GC_2PCF_IO/LE3_GC_2PCF_Output.py

:date: 04/06/22
:author: user

"""

import os
import json
import argparse

import ElementsKernel.Logging as log

from LE3_GC_2PCF_IO import LE3_GC_2PCF_IO_CreateProduct


def defineSpecificProgramOptions():
    """
    @brief Allows to define the (command line and configuration file) options
    specific to this program

    @details See the Elements documentation for more details.
    @return An  ArgumentParser.
    """

    parser = argparse.ArgumentParser()

    parser.add_argument('--workdir',        help='name of the working directory')
    parser.add_argument('--logdir',         help='name of the logging directory')
    parser.add_argument('--euclid_results', required=True, help='json file with output datacontainers')
    parser.add_argument('--output_file',    required=True, help='output XML product')

    return parser


def mainMethod(args):
    """
    @brief The "main" method.
    @details This method is the entry point to the program. In this sense, it is
    similar to a main (and it is why it is called mainMethod()).
    """

    module = 'LE3_GC_2PCF_Output'
    logger = log.getLogger(module)

    logger.info('#')
    logger.info(f'# Entering {module}')
    logger.info('#')
 
    create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
    
    euclid_results = os.path.join(args.workdir, args.euclid_results)
    
    logger.info(f'Loading 2PCF products names from {euclid_results}')
    with open(euclid_results) as json_file:
        filenames = json.load(json_file)
        # append all path to filenames
        # filenames = {name : os.path.join(args.workdir, filename) for name, filename in tag_dict.items()}
    
    create_product.prepare_product(filenames)
    
    create_product.write_xml(os.path.join(args.workdir, args.output_file))

    logger.info('#')
    logger.info(f'# Exiting {module}')
    logger.info('#')
