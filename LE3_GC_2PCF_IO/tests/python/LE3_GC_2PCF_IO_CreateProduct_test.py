#
# Copyright (C) 2012-2020 Euclid Science Ground Segment
#
# This library is free software; you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation; either version 3.0 of the License, or (at your option)
# any later version.
#
# This library is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON>ILITY or FITNESS
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this library; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
#

"""
:file: tests/python/LE3_GC_2PCF_IO_CreateProduct_test.py

:date: 04/12/22
:author: <PERSON><PERSON> <PERSON><PERSON><PERSON>
"""

from astropy.io import fits
from lxml import etree
import os
import pathlib
import pytest
import copy
import lxml
import tempfile
from LE3_GC_2PCF_IO import LE3_GC_2PCF_IO_CreateProduct

from ST_DATAMODEL_INSTALL import ST_DATAMODEL_INSTALL_LOCATION
from ST_DATAMODEL_VERSION import ST_DATAMODEL_ORIGINAL_VERSION



class TestLE3_GC_2PCF_IO_CreateProduct():

    def mop_up(self, dic_file):
        for file in dic_file.values():
            print(file)
            os.unlink(file)
    # ----------------------------------------------------------------------- #


    def write_test_fitsfile(self, filename:str, extname:str, header:dict):
        """_summary_

        Args:
            filename (str): _description_
            extname (str): _description_
            header (dict): _description_
        """
        bint = fits.BinTableHDU()
        bint.header['EXTNAME'] = extname
    
        for key in header.keys():
            bint.header[key] = header[key]
        hdul = fits.HDUList([fits.PrimaryHDU(), bint])
        hdul.writeto(filename)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #


    def testPrepare_product_auto(self):
        """test the AUTO case"""
        CORR_HEADER = {'D_NAME'   : 'dummy',
                       'R_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN_TYPE' : 'LOG',
                       'BIN_NUM'  : '10',
                       'BIN_MIN'  : '1',
                       'BIN_MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'AutoCorrelationFile': 'testAutoCorr.fits', 'AutoPairsFile': 'testAutoPairs.fits'}
        
        self.write_test_fitsfile(filenames['AutoCorrelationFile'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['AutoPairsFile'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-auto.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_cart(self):
        """test the AUTO case"""
        CORR_HEADER = {'D_NAME'   : 'dummy',
                       'R_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'PI_MAX'   : '6',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'AutoCorrelationFile2Dcart': 'testAutoCorr2D.fits',
                     'AutoCorrelationFile2Dproj': 'testAutoCorrProj.fits',
                     'AutoPairsFile2D'          : 'testAutoPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['AutoCorrelationFile2Dcart'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['AutoCorrelationFile2Dproj'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['AutoPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-auto-cart.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_pol(self):
        """test the AUTO case"""
        CORR_HEADER = {'D_NAME'   : 'dummy',
                       'R_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'LOS_DEF'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'AutoCorrelationFile2Dpolar': 'testAutoCorr2D.fits',
                     'AutoCorrelationFile2Dmulti': 'testAutoCorrMulti.fits',
                     'AutoPairsFile2D'           : 'testAutoPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['AutoCorrelationFile2Dpolar'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['AutoCorrelationFile2Dmulti'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['AutoPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-auto-pol.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
        
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #


    def testPrepare_product_cross(self):
        """test the AUTO case"""
        CORR_HEADER = {'D1_NAME'   : 'dummy',
                       'R1_NAME'   : 'dummy',
                       'D2_NAME'   : 'dummy',
                       'R2_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT1'   : 'dummy',
                       'SELECT2'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN_TYPE' : 'LOG',
                       'BIN_NUM'  : '10',
                       'BIN_MIN'  : '1',
                       'BIN_MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'CrossCorrelationFile': 'testCrossCorr.fits', 'CrossPairsFile': 'testCrossPairs.fits'}
        
        self.write_test_fitsfile(filenames['CrossCorrelationFile'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossPairsFile'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-cross.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_cross_cart(self):
        """test the AUTO case"""
        CORR_HEADER = {'D1_NAME'   : 'dummy',
                       'R1_NAME'   : 'dummy',
                       'D2_NAME'   : 'dummy',
                       'R2_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT1'   : 'dummy',
                       'SELECT2'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'PI_MAX'   : '6',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'CrossCorrelationFile2Dcart': 'testCrossCorr2D.fits',
                     'CrossCorrelationFile2Dproj': 'testCrossCorrProj.fits',
                     'CrossPairsFile2D'          : 'testCrossPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['CrossCorrelationFile2Dcart'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossCorrelationFile2Dproj'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-cross-cart.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_cross_pol(self):
        """test the AUTO case"""
        CORR_HEADER = {'D1_NAME'   : 'dummy',
                       'R1_NAME'   : 'dummy',
                       'D2_NAME'   : 'dummy',
                       'R2_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT1'   : 'dummy',
                       'SELECT2'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'LOS_DEF'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'CrossCorrelationFile2Dpolar': 'testCrossCorr2D.fits',
                     'CrossCorrelationFile2Dmulti': 'testCrossCorrMulti.fits',
                     'CrossPairsFile2D'           : 'testCrossPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['CrossCorrelationFile2Dpolar'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossCorrelationFile2Dmulti'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-cross-pol.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_rec(self):
        """test the AUTO case"""
        CORR_HEADER = {'D_NAME'   : 'dummy',
                       'R_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN_TYPE' : 'LOG',
                       'BIN_NUM'  : '10',
                       'BIN_MIN'  : '1',
                       'BIN_MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'ReconCorrelationFile': 'testRecCorr.fits', 'ReconPairsFile': 'testRecPairs.fits'}
        
        self.write_test_fitsfile(filenames['ReconCorrelationFile'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['ReconPairsFile'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-rec-auto.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_rec_cart(self):
        """test the AUTO case"""
        CORR_HEADER = {'D_NAME'   : 'dummy',
                       'R_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'PI_MAX'   : '6',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'ReconCorrelationFile2Dcart': 'testRecCorr2D.fits',
                     'ReconCorrelationFile2Dproj': 'testRecCorrProj.fits',
                     'ReconPairsFile2D'          : 'testRecPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['ReconCorrelationFile2Dcart'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['ReconCorrelationFile2Dproj'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['ReconPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-rec-auto-cart.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_rec_pol(self):
        """test the AUTO case"""
        CORR_HEADER = {'D_NAME'   : 'dummy',
                       'R_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'LOS_DEF'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'ReconCorrelationFile2Dpolar': 'testRecCorr2D.fits',
                     'ReconCorrelationFile2Dmulti': 'testRecCorrMulti.fits',
                     'ReconPairsFile2D'           : 'testRecPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['ReconCorrelationFile2Dpolar'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['ReconCorrelationFile2Dmulti'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['ReconPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-rec-auto-pol.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
        
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    def testPrepare_product_cross_rec(self):
        """test the AUTO case"""
        CORR_HEADER = {'D1_NAME'   : 'dummy',
                       'R1_NAME'   : 'dummy',
                       'D2_NAME'   : 'dummy',
                       'R2_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT1'   : 'dummy',
                       'SELECT2'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN_TYPE' : 'LOG',
                       'BIN_NUM'  : '10',
                       'BIN_MIN'  : '1',
                       'BIN_MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'CrossReconCorrelationFile': 'testCrossRecCorr.fits', 'CrossReconPairsFile': 'testCrossRecPairs.fits'}
        
        self.write_test_fitsfile(filenames['CrossReconCorrelationFile'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossReconPairsFile'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-rec-cross.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_cross_rec_cart(self):
        """test the AUTO case"""
        CORR_HEADER = {'D1_NAME'   : 'dummy',
                       'R1_NAME'   : 'dummy',
                       'D2_NAME'   : 'dummy',
                       'R2_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT1'   : 'dummy',
                       'SELECT2'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'PI_MAX'   : '6',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'CrossReconCorrelationFile2Dcart': 'testCrossRecCorr2D.fits',
                     'CrossReconCorrelationFile2Dproj': 'testCrossRecCorrProj.fits',
                     'CrossReconPairsFile2D'          : 'testCrossRecPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['CrossReconCorrelationFile2Dcart'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossReconCorrelationFile2Dproj'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossReconPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-rec-cross-cart.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
    
    
    def testPrepare_product_cross_rec_pol(self):
        """test the AUTO case"""
        CORR_HEADER = {'D1_NAME'   : 'dummy',
                       'R1_NAME'   : 'dummy',
                       'D2_NAME'   : 'dummy',
                       'R2_NAME'   : 'dummy',
                       'RUNTYPE'  : 'dummy',
                       'SELECT1'   : 'dummy',
                       'SELECT2'   : 'dummy',
                       'STAT'     : 'ONE_DIM',
                       'METHOD'   : 'LINKED_LIST',
                       'BIN1TYPE' : 'LOG',
                       'BIN1NUM'  : '10',
                       'BIN1MIN'  : '1',
                       'BIN1MAX'  : '11',
                       'BIN2TYPE' : 'LOG',
                       'BIN2NUM'  : '10',
                       'BIN2MIN'  : '1',
                       'BIN2MAX'  : '11',
                       'WEIGHT'   : 'true',
                       'SPLIT'    : '100',
                       'COSMO_ID' : 'dummy',
                       'RELEASE'  : 'dummy',
                       'LOS_DEF'  : 'dummy',
                       'BLINDING' : ' '}
        
        filenames = {'CrossReconCorrelationFile2Dpolar': 'testCrossCorr2D.fits',
                     'CrossReconCorrelationFile2Dmulti': 'testCrossCorrMulti.fits',
                     'CrossReconPairsFile2D'           : 'testCrossPairs2D.fits'}
        
        self.write_test_fitsfile(filenames['CrossReconCorrelationFile2Dpolar'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossReconCorrelationFile2Dmulti'],  'CORRELATION', CORR_HEADER)
        self.write_test_fitsfile(filenames['CrossReconPairsFile2D'], 'PAIRS',  CORR_HEADER)
        
        create_product = LE3_GC_2PCF_IO_CreateProduct.LE3_GC_2PCF_IO_CreateProduct()
        create_product.prepare_product(filenames)
 
        xsdfile = f"{ST_DATAMODEL_INSTALL_LOCATION}/auxdir/ST_DM_Schema/dpd/le3/gc/euc-le3-gc-2pcf-rec-cross-pol.xsd"
        
        xmlschema_doc = etree.parse(xsdfile)
        xmlschema     = etree.XMLSchema(xmlschema_doc)
        xmlschema.assertValid(create_product._xml)

        self.mop_up(filenames)
    # ----------------------------------------------------------------------- #
    # ----------------------------------------------------------------------- #
