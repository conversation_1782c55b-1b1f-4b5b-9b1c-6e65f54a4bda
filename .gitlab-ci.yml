image: eden:3

variables:
    GIT_CLEAN_FLAGS: none
    GIT_STRATEGY: fetch

stages:
      - status
      - build
      - test
#      - doc

before_script:
      - source /cvmfs/euclid-dev.in2p3.fr/EDEN-3.1/bin/activate
      - git clean -ffdx  --exclude=".status/" 

status:
    stage: status
    tags:
      - SDCIT_QV
    only:
      - $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
    script:
      - make configure
      - /opt/prepare_badge.sh
    artifacts:
      paths:
        - .status/*.svg

build:
    stage: build
    tags:
      - SDCIT_QV
    only: 
      - main
      - develop
      - $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      - /feature.*/
    script:
      - make purge
      - make -j 8

test:
    stage: test
    tags:
      - SDCIT_QV
    only: 
      - main
      - develop
      - $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      - /feature.*/
    script:
      - make -j 8 install
      - make test
#    allow_failure: true

#doc:
#    stage: doc
#    only:
#      - master
#    script:
#      - make doc

