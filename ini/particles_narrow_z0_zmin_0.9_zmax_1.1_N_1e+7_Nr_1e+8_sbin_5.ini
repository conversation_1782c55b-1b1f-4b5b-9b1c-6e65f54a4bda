# ==============================================================================
#
#  Basic parameter file for the 2-point correlation function
#
# This parameter file has the following format:
# [Section.Subsection]
#    parameter_name = parameter_value
#
# Lines starting with # are considered comments and ignored
#
# LE3GC Version >= 5.0.0
#
# =========================================================
# ---------------------------------------------------------------------------- #
#                        PARAMETERS FOR CATALOGS
# ---------------------------------------------------------------------------- #
[Catalog]

[Catalog.Galaxy]
  # input file for galaxy catalog with path relative to input_path
  # filename = LE3_GC_2PCF/tests/examples/data/galaxy_coordinates.dat
  filename = catalogs/raygal/diluted/xrand/bins/particles_catalog_1.00e+07_narrow_zmin_0.9_zmax_1.1_z0_comov_dist.txt

  # name of HDU in FITS (if FITS input used)
  # name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = EQUATORIAL

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = None

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = None

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = None


[Catalog.Random]
  # input file for random catalog with path relative to input_path
  # filename = LE3_GC_2PCF/tests/examples/data/random_coordinates.dat
  filename = catalogs/raygal/randoms/xrand/bins/random_catalog_1.00e+08_narrow_zmin_0.9_zmax_1.1_z0_comov_dist.txt

  # name of HDU in FITS (if FITS input used)
  #name = 

  # Coordinate system (for ASCII) or coordinate system keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     CARTESIAN: x1, x2, x3
  #     EUQATORIAL: RA, Dec, r (radius, or distance)
  #     PSEUDO_EQUATORIAL: RA, Dec, redshift
  #
  coordinates = EQUATORIAL

  # Angle units for EQUATORIAL/PSEUDO_EQUATORIAL coordinates (for ASCII)
  #  or angle units keyword name (for FITS)
  # - ASCII file valid coordinate types:
  #     DEGREES
  #     RADIANS
  #
  angle_units = DEG

  # Object coordinates: Column number (for ASCII) OR column name (for FITS)
  coord1      = 1
  coord2      = 2
  coord3      = 3

  # Object mean density: Column number (for ASCII) OR column name (for FITS)
  density     = None

  # Object weight: Column number (for ASCII) OR column name (for FITS)
  weight      = None

  # Object mask: Column number (for ASCII) OR column name (for FITS)
  mask        = None

#------------------------------------------------------------------------------#
#             PARAMETERS FOR THE 2PT CORRELATION FUNCTION
#------------------------------------------------------------------------------#
[2PCF]
  # Correlation type [AUTO_1D, AUTO_2DCART, AUTO_2DPOL]
  statistics = AUTO_1D
  # Pair counting method [LINKED_LIST, KD_TREE, OCTREE]
  method     = LINKED_LIST

  # Type of binning along 1st dimension [LIN, LOG]
  bin1_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin1_num   = 20
  # Minimum value in the 1st dimension (double)
  bin1_min   = 27.5
  # Maximum value in the 1st dimension (double)
  bin1_max   = 127.5

  # Type of binning along 2nd dimension [LINEAR, LOG]
  bin2_type  = LIN
  # Number of bin channels in the 1st dimension (integer)
  bin2_num   = 100
  # Minimum value in the 1st dimension (double)
  bin2_min   = 0.1
  # Maximum value in the 1st dimension (double)
  bin2_max   = 10.

  PI_MAX = 50.0

  # prefix name for output files
  output_prefix = particles_narrow_z0_zmin_0.9_zmax_1.1_N_1e+7_Nr_1e+8_sbin_5 

  compute_DD = true
  compute_DR = true
  compute_RR = true

  # Input pairs FITS file (with path relative to workdir) if skip DD calculation
  input_pairs = 

  # Use object weights [true, false]
  use_weight = false

  # enable legacy TXT output files
  ASCII_out = true
  enableTXT = true

  # Split mode: 1=split off
  split_factor = 3

  # Rescale grid
  grid_scale = 1

#Set coordinate system of galaxy cat. [0=CARTESIAN, 1= EUQATORIAL, 2=PSEUDOEQUATORIAL (ra,dec,z)]
sys_of_coord_g = 1
# Set units of angles for EQUATORIAL coordinates: [DEG, RAD]
angles_units_g = DEG

# Column number of the three coordinates in the DATA catalog
i_coord1_g = 1
i_coord2_g = 2
i_coord3_g = 3

# RANDOM catalog
# [Random]

use_random_catalog = yes

#Set coordinate system of random cat.
sys_of_coord_r = 1
# Set units of angles for EQUATORIAL coordinates: [DEG, RAD]
angles_units_r = DEG

# Column number of the three coordinates in the catalog
i_coord1_r = 1
i_coord2_r = 2
i_coord3_r = 3


# ---------------------------------------------------------------------------- #
#                   COSMOLOGICAL PARAMETERS FOR Z CONVERSION
# ---------------------------------------------------------------------------- #
# Parameters used to transform redshift to comoving distance

[Cosmology]

  # ID to identify the cosmological model assumed
  cosmology_ID = LCDM

  # Matter energy density parameter
  om_matter = 0.257330000

  # Radiation energy density parameter
  om_radiation = 0.000080763
 
  # Baryon energy density parameter
  om_baryons = 0.043557099

  # Vacuum energy density parameter
  om_vac = 0.742589237

  # Curvature energy density parameter
  om_k = 0.0

  # Hubble parameter in units of 100 Km /s /(Mpc /h)
  Hubble = 72.0

  # Dimensionless Hubble parameter
  hubble = 0.72000000

  # Scalar index of primordial perturbations
  spectral_index = 0.96300000

  # Dark energy equation of state
  w_eos = -1.0

  # Effective number of relativistic species
  N_eff = 3.03

  # Mass dipersion on sphers of 8 Mpc/h radius
  sigma8 = 0.80100775

  # CMB temperature
  Tcmb = 2.73
# ---------------------------------------------------------------------------- #
